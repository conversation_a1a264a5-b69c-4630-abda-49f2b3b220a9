<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الاشتراكات - صفحة الاختبار</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        h1 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.2rem;
        }
        
        .status {
            background: #e8f5e8;
            color: #2d5a2d;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            border: 2px solid #4caf50;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .feature p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 2rem;
            border-right: 4px solid #2196f3;
        }
        
        .server-info {
            background: #fff3e0;
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            border-right: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏢</div>
        <h1>نظام إدارة الاشتراكات</h1>
        <p class="subtitle">نظام متكامل لإدارة الاشتراكات والعملاء والفواتير</p>
        
        <div class="status">
            <strong>✅ النظام يعمل بنجاح!</strong><br>
            تم تشغيل الخادم على المنفذ 9876
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>📊 لوحة المعلومات</h3>
                <p>إحصائيات شاملة ورسوم بيانية تفاعلية</p>
            </div>
            <div class="feature">
                <h3>📋 إدارة الاشتراكات</h3>
                <p>إنشاء وتتبع وتجديد الاشتراكات</p>
            </div>
            <div class="feature">
                <h3>👥 إدارة العملاء</h3>
                <p>قاعدة بيانات شاملة للعملاء</p>
            </div>
            <div class="feature">
                <h3>🧾 إدارة الفواتير</h3>
                <p>إصدار وتتبع الفواتير والمدفوعات</p>
            </div>
        </div>
        
        <div class="buttons">
            <a href="http://localhost:9876/api/health" class="btn btn-primary" target="_blank">
                🔍 فحص حالة الخادم
            </a>
            <a href="http://localhost:9876/api/test" class="btn btn-secondary" target="_blank">
                🧪 اختبار API
            </a>
        </div>
        
        <div class="info">
            <strong>📋 معلومات النظام:</strong><br>
            • الخادم: http://localhost:9876<br>
            • حالة قاعدة البيانات: قيد الإعداد<br>
            • الإصدار: 1.0.0
        </div>
        
        <div class="server-info">
            <strong>⚙️ الخطوات التالية:</strong><br>
            1. تأكد من تشغيل MongoDB<br>
            2. قم بتشغيل العميل (Frontend)<br>
            3. أنشئ مستخدم مدير للنظام
        </div>
    </div>
    
    <script>
        // Test API connectivity
        fetch('/api/health')
            .then(response => response.json())
            .then(data => {
                console.log('✅ API يعمل بنجاح:', data);
            })
            .catch(error => {
                console.error('❌ خطأ في الاتصال بـ API:', error);
            });
    </script>
</body>
</html>
