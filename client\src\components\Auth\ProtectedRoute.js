import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';

const LoadingContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background);
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const LoadingText = styled.p`
  margin-top: var(--spacing-lg);
  color: var(--color-textSecondary);
  font-size: var(--text-base);
`;

const LoadingWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
`;

const ProtectedRoute = ({ children, requiredPermission, requiredRole }) => {
  const { isAuthenticated, loading, user, hasPermission, hasRole } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <LoadingContainer>
        <LoadingWrapper>
          <LoadingSpinner />
          <LoadingText>جاري التحقق من الصلاحيات...</LoadingText>
        </LoadingWrapper>
      </LoadingContainer>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user account is active
  if (user && !user.isActive) {
    return (
      <LoadingContainer>
        <LoadingWrapper>
          <LoadingText>حسابك غير نشط. يرجى التواصل مع المدير.</LoadingText>
        </LoadingWrapper>
      </LoadingContainer>
    );
  }

  // Check required role if specified
  if (requiredRole && !hasRole(requiredRole)) {
    return (
      <LoadingContainer>
        <LoadingWrapper>
          <LoadingText>ليس لديك الصلاحية للوصول إلى هذه الصفحة.</LoadingText>
        </LoadingWrapper>
      </LoadingContainer>
    );
  }

  // Check required permission if specified
  if (requiredPermission) {
    const { resource, action } = requiredPermission;
    if (!hasPermission(resource, action)) {
      return (
        <LoadingContainer>
          <LoadingWrapper>
            <LoadingText>ليس لديك الصلاحية للوصول إلى هذه الصفحة.</LoadingText>
          </LoadingWrapper>
        </LoadingContainer>
      );
    }
  }

  // Render children if all checks pass
  return children;
};

export default ProtectedRoute;
