#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 بدء تشغيل نظام إدارة الاشتراكات...\n');

// Check if .env file exists
const envPath = path.join(__dirname, 'server', '.env');
if (!fs.existsSync(envPath)) {
  console.log('⚠️  ملف .env غير موجود في مجلد server');
  console.log('📝 يرجى نسخ ملف .env.example إلى .env وتعديل الإعدادات\n');
  
  const examplePath = path.join(__dirname, 'server', '.env.example');
  if (fs.existsSync(examplePath)) {
    try {
      fs.copyFileSync(examplePath, envPath);
      console.log('✅ تم إنشاء ملف .env من النموذج');
      console.log('📝 يرجى تعديل الإعدادات في server/.env قبل المتابعة\n');
    } catch (error) {
      console.log('❌ فشل في إنشاء ملف .env:', error.message);
    }
  }
}

// Check if node_modules exist
const serverNodeModules = path.join(__dirname, 'server', 'node_modules');
const clientNodeModules = path.join(__dirname, 'client', 'node_modules');

if (!fs.existsSync(serverNodeModules) || !fs.existsSync(clientNodeModules)) {
  console.log('📦 تثبيت التبعيات...');
  
  const installProcess = spawn('npm', ['run', 'install-all'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });
  
  installProcess.on('close', (code) => {
    if (code === 0) {
      console.log('✅ تم تثبيت التبعيات بنجاح\n');
      startApplication();
    } else {
      console.log('❌ فشل في تثبيت التبعيات');
      process.exit(1);
    }
  });
} else {
  startApplication();
}

function startApplication() {
  console.log('🔧 بدء تشغيل الخادم والعميل...\n');
  
  // Start the development servers
  const devProcess = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  });
  
  devProcess.on('close', (code) => {
    console.log(`\n📊 انتهى التطبيق بالكود: ${code}`);
  });
  
  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف التطبيق...');
    devProcess.kill('SIGINT');
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف التطبيق...');
    devProcess.kill('SIGTERM');
    process.exit(0);
  });
  
  // Display startup information
  setTimeout(() => {
    console.log('\n📋 معلومات التشغيل:');
    console.log('🌐 العميل (Frontend): http://localhost:3000');
    console.log('🔧 الخادم (Backend): http://localhost:5000');
    console.log('📚 API Documentation: http://localhost:5000/api');
    console.log('\n💡 نصائح:');
    console.log('- تأكد من تشغيل MongoDB قبل استخدام النظام');
    console.log('- يمكنك إنشاء مستخدم مدير من خلال API /api/auth/register');
    console.log('- راجع ملف README.md للمزيد من التفاصيل');
    console.log('\n⌨️  اضغط Ctrl+C لإيقاف التطبيق');
  }, 3000);
}
