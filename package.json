{"name": "subscription-management-system", "version": "1.0.0", "description": "نظام إدارة الاشتراكات المتكامل", "main": "server/index.js", "scripts": {"start": "node start.js", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "build": "cd client && npm run build", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "setup": "npm run install-all && echo 'تم إعداد المشروع بنجاح! استخدم npm start للتشغيل'"}, "keywords": ["subscription", "management", "billing", "react", "nodejs", "mongodb"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}