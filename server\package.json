{"name": "subscription-management-server", "version": "1.0.0", "description": "Backend server for subscription management system", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "moment": "^2.29.4", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "api", "subscription"], "author": "<PERSON>", "license": "MIT"}