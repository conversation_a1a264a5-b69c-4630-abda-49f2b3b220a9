const mongoose = require('mongoose');

const invoiceSchema = new mongoose.Schema({
  invoiceNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'العميل مطلوب']
  },
  subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription',
    required: [true, 'الاشتراك مطلوب']
  },
  issueDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  paidDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['draft', 'sent', 'paid', 'overdue', 'cancelled', 'refunded'],
    default: 'draft'
  },
  items: [{
    description: {
      type: String,
      required: true,
      trim: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0,
      default: 1
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    taxRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    taxAmount: {
      type: Number,
      min: 0,
      default: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    }
  }],
  subtotal: {
    type: Number,
    required: true,
    min: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  taxTotal: {
    type: Number,
    min: 0,
    default: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  discountAmount: {
    type: Number,
    min: 0,
    default: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  currency: {
    type: String,
    enum: ['USD', 'EUR', 'SAR', 'AED', 'EGP'],
    default: 'USD'
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'bank_transfer', 'paypal', 'cash', 'check'],
    default: 'credit_card'
  },
  paymentDetails: {
    transactionId: { type: String },
    paymentGateway: { type: String },
    cardLast4: { type: String },
    bankReference: { type: String }
  },
  billingAddress: {
    name: { type: String },
    company: { type: String },
    street: { type: String },
    city: { type: String },
    state: { type: String },
    country: { type: String },
    zipCode: { type: String }
  },
  notes: {
    type: String,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  internalNotes: {
    type: String,
    maxlength: [1000, 'الملاحظات الداخلية يجب أن تكون أقل من 1000 حرف']
  },
  attachments: [{
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    uploadDate: { type: Date, default: Date.now }
  }],
  emailSent: {
    sent: { type: Boolean, default: false },
    sentDate: { type: Date },
    sentTo: { type: String },
    attempts: { type: Number, default: 0 }
  },
  reminders: [{
    sentDate: { type: Date, required: true },
    type: { 
      type: String, 
      enum: ['email', 'sms', 'phone'], 
      required: true 
    },
    message: { type: String },
    sentBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for better performance
invoiceSchema.index({ customer: 1 });
invoiceSchema.index({ subscription: 1 });
invoiceSchema.index({ status: 1 });
invoiceSchema.index({ dueDate: 1 });
invoiceSchema.index({ issueDate: -1 });
invoiceSchema.index({ invoiceNumber: 1 });

// Auto-generate invoice number
invoiceSchema.pre('save', async function(next) {
  if (this.isNew && !this.invoiceNumber) {
    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Find the last invoice for this month
    const lastInvoice = await this.constructor.findOne({
      invoiceNumber: new RegExp(`^INV-${currentYear}${currentMonth}-`)
    }).sort({ invoiceNumber: -1 });
    
    let nextNumber = 1;
    if (lastInvoice) {
      const lastNumber = parseInt(lastInvoice.invoiceNumber.split('-')[2]);
      nextNumber = lastNumber + 1;
    }
    
    this.invoiceNumber = `INV-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`;
  }
  next();
});

// Calculate totals before saving
invoiceSchema.pre('save', function(next) {
  // Calculate item totals
  this.items.forEach(item => {
    item.totalPrice = item.quantity * item.unitPrice;
    item.taxAmount = (item.totalPrice * item.taxRate) / 100;
  });
  
  // Calculate subtotal
  this.subtotal = this.items.reduce((sum, item) => sum + item.totalPrice, 0);
  
  // Calculate tax total
  this.taxTotal = this.items.reduce((sum, item) => sum + item.taxAmount, 0);
  
  // Calculate total amount
  this.totalAmount = this.subtotal + this.taxTotal - this.discountAmount;
  
  // Round to 2 decimal places
  this.subtotal = Math.round(this.subtotal * 100) / 100;
  this.taxTotal = Math.round(this.taxTotal * 100) / 100;
  this.totalAmount = Math.round(this.totalAmount * 100) / 100;
  
  next();
});

// Method to mark invoice as paid
invoiceSchema.methods.markAsPaid = function(paymentDetails = {}) {
  this.status = 'paid';
  this.paidDate = new Date();
  if (paymentDetails) {
    this.paymentDetails = { ...this.paymentDetails, ...paymentDetails };
  }
  return this.save();
};

// Method to check if invoice is overdue
invoiceSchema.methods.isOverdue = function() {
  return this.status !== 'paid' && new Date() > this.dueDate;
};

// Virtual for days overdue
invoiceSchema.virtual('daysOverdue').get(function() {
  if (!this.isOverdue()) return 0;
  const diffTime = new Date() - this.dueDate;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

module.exports = mongoose.model('Invoice', invoiceSchema);
