import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import styled from 'styled-components';
import Sidebar from './Sidebar';
import Header from './Header';
import { useTheme } from '../../contexts/ThemeContext';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
  background-color: var(--color-background);
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const MainContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: ${props => props.isRTL && !props.sidebarCollapsed ? '280px' : '0'};
  margin-left: ${props => !props.isRTL && !props.sidebarCollapsed ? '280px' : '0'};
  margin-right: ${props => props.isRTL && props.sidebarCollapsed ? '80px' : props.isRTL ? '280px' : '0'};
  margin-left: ${props => !props.isRTL && props.sidebarCollapsed ? '80px' : !props.isRTL ? '280px' : '0'};
  transition: margin 0.3s ease;
  
  @media (max-width: 768px) {
    margin-right: 0;
    margin-left: 0;
  }
`;

const ContentArea = styled.main`
  flex: 1;
  padding: var(--spacing-lg);
  background-color: var(--color-backgroundSecondary);
  min-height: calc(100vh - 70px);
  
  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
`;

const Layout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { isRTL } = useTheme();

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <LayoutContainer isRTL={isRTL}>
      <Sidebar 
        collapsed={sidebarCollapsed}
        mobileOpen={sidebarOpen}
        onToggle={toggleSidebar}
        onMobileToggle={toggleMobileSidebar}
      />
      
      <MainContent 
        sidebarCollapsed={sidebarCollapsed}
        isRTL={isRTL}
      >
        <Header 
          onToggleSidebar={toggleSidebar}
          onToggleMobileSidebar={toggleMobileSidebar}
          sidebarCollapsed={sidebarCollapsed}
        />
        
        <ContentArea>
          <Outlet />
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};

export default Layout;
