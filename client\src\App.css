/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif, 'Noto Sans Arabic', 'Cairo', 'Amiri';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--color-textPrimary);
  background-color: var(--color-background);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Arabic font support */
[lang="ar"], [dir="rtl"] {
  font-family: 'Noto Sans Arabic', 'Cairo', 'Amiri', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Code font */
code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-backgroundSecondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-borderDark);
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background-color: var(--color-primary);
  color: white;
}

/* Link styles */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primaryHover);
}

/* Button reset */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* Input reset */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
}

/* Table styles */
table {
  border-collapse: collapse;
  width: 100%;
}

th, td {
  text-align: right;
  padding: 8px;
  border-bottom: 1px solid var(--color-border);
}

th {
  background-color: var(--color-backgroundTertiary);
  font-weight: 600;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

/* Theme-specific styles */
.theme-light {
  --color-primary: #2563eb;
  --color-primaryHover: #1d4ed8;
  --color-secondary: #64748b;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  --color-background: #ffffff;
  --color-backgroundSecondary: #f8fafc;
  --color-backgroundTertiary: #f1f5f9;
  
  --color-textPrimary: #1e293b;
  --color-textSecondary: #64748b;
  --color-textMuted: #94a3b8;
  --color-textInverse: #ffffff;
  
  --color-border: #e2e8f0;
  --color-borderLight: #f1f5f9;
  --color-borderDark: #cbd5e1;
  
  --color-sidebarBg: #1e293b;
  --color-sidebarText: #cbd5e1;
  --color-sidebarTextActive: #ffffff;
  --color-sidebarHover: #334155;
  
  --color-cardBg: #ffffff;
  --color-cardBorder: #e2e8f0;
  --color-cardShadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.theme-dark {
  --color-primary: #3b82f6;
  --color-primaryHover: #2563eb;
  --color-secondary: #6b7280;
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
  
  --color-background: #0f172a;
  --color-backgroundSecondary: #1e293b;
  --color-backgroundTertiary: #334155;
  
  --color-textPrimary: #f8fafc;
  --color-textSecondary: #cbd5e1;
  --color-textMuted: #94a3b8;
  --color-textInverse: #1e293b;
  
  --color-border: #334155;
  --color-borderLight: #475569;
  --color-borderDark: #1e293b;
  
  --color-sidebarBg: #0f172a;
  --color-sidebarText: #94a3b8;
  --color-sidebarTextActive: #f8fafc;
  --color-sidebarHover: #1e293b;
  
  --color-cardBg: #1e293b;
  --color-cardBorder: #334155;
  --color-cardShadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

/* Responsive design */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}
