const express = require('express');
const Subscription = require('../models/Subscription');
const Customer = require('../models/Customer');
const Invoice = require('../models/Invoice');
const Payment = require('../models/Payment');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/reports/dashboard
// @desc    Get dashboard summary report
// @access  Private (requires reports view permission)
router.get('/dashboard', auth, checkPermission('reports', 'view'), async (req, res) => {
  try {
    // Get current date ranges
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const currentYear = new Date(now.getFullYear(), 0, 1);

    // Subscriptions summary
    const subscriptionsStats = {
      total: await Subscription.countDocuments(),
      active: await Subscription.countDocuments({ status: 'active' }),
      inactive: await Subscription.countDocuments({ status: 'inactive' }),
      suspended: await Subscription.countDocuments({ status: 'suspended' }),
      cancelled: await Subscription.countDocuments({ status: 'cancelled' }),
      thisMonth: await Subscription.countDocuments({ 
        createdAt: { $gte: currentMonth } 
      }),
      lastMonth: await Subscription.countDocuments({ 
        createdAt: { $gte: lastMonth, $lt: currentMonth } 
      })
    };

    // Customers summary
    const customersStats = {
      total: await Customer.countDocuments(),
      active: await Customer.countDocuments({ status: 'active' }),
      inactive: await Customer.countDocuments({ status: 'inactive' }),
      thisMonth: await Customer.countDocuments({ 
        registrationDate: { $gte: currentMonth } 
      }),
      lastMonth: await Customer.countDocuments({ 
        registrationDate: { $gte: lastMonth, $lt: currentMonth } 
      })
    };

    // Revenue summary
    const revenueStats = await Payment.aggregate([
      {
        $match: { status: 'completed' }
      },
      {
        $facet: {
          total: [
            { $group: { _id: null, amount: { $sum: '$amount' } } }
          ],
          thisMonth: [
            { $match: { paymentDate: { $gte: currentMonth } } },
            { $group: { _id: null, amount: { $sum: '$amount' } } }
          ],
          lastMonth: [
            { $match: { paymentDate: { $gte: lastMonth, $lt: currentMonth } } },
            { $group: { _id: null, amount: { $sum: '$amount' } } }
          ],
          thisYear: [
            { $match: { paymentDate: { $gte: currentYear } } },
            { $group: { _id: null, amount: { $sum: '$amount' } } }
          ]
        }
      }
    ]);

    const revenue = {
      total: revenueStats[0].total[0]?.amount || 0,
      thisMonth: revenueStats[0].thisMonth[0]?.amount || 0,
      lastMonth: revenueStats[0].lastMonth[0]?.amount || 0,
      thisYear: revenueStats[0].thisYear[0]?.amount || 0
    };

    // Invoices summary
    const invoicesStats = {
      total: await Invoice.countDocuments(),
      paid: await Invoice.countDocuments({ status: 'paid' }),
      pending: await Invoice.countDocuments({ status: { $in: ['draft', 'sent'] } }),
      overdue: await Invoice.countDocuments({ 
        status: { $ne: 'paid' },
        dueDate: { $lt: now }
      }),
      thisMonth: await Invoice.countDocuments({ 
        issueDate: { $gte: currentMonth } 
      })
    };

    res.json({
      subscriptions: subscriptionsStats,
      customers: customersStats,
      revenue,
      invoices: invoicesStats,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get dashboard report error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/reports/revenue
// @desc    Get revenue report
// @access  Private (requires reports view permission)
router.get('/revenue', auth, checkPermission('reports', 'view'), async (req, res) => {
  try {
    const { period = 'monthly', year = new Date().getFullYear() } = req.query;

    let groupBy, dateFormat;
    if (period === 'daily') {
      groupBy = { $dayOfYear: '$paymentDate' };
      dateFormat = { $dateToString: { format: '%Y-%m-%d', date: '$paymentDate' } };
    } else if (period === 'weekly') {
      groupBy = { $week: '$paymentDate' };
      dateFormat = { $dateToString: { format: '%Y-W%U', date: '$paymentDate' } };
    } else {
      groupBy = { $month: '$paymentDate' };
      dateFormat = { $dateToString: { format: '%Y-%m', date: '$paymentDate' } };
    }

    const startDate = new Date(year, 0, 1);
    const endDate = new Date(parseInt(year) + 1, 0, 1);

    const revenueData = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: groupBy,
          totalRevenue: { $sum: '$amount' },
          totalPayments: { $sum: 1 },
          date: { $first: dateFormat }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Revenue by payment method
    const revenueByMethod = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: '$paymentMethod',
          totalRevenue: { $sum: '$amount' },
          totalPayments: { $sum: 1 }
        }
      }
    ]);

    res.json({
      period,
      year,
      revenueData,
      revenueByMethod,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get revenue report error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/reports/subscriptions
// @desc    Get subscriptions report
// @access  Private (requires reports view permission)
router.get('/subscriptions', auth, checkPermission('reports', 'view'), async (req, res) => {
  try {
    const { period = 'monthly', year = new Date().getFullYear() } = req.query;

    const startDate = new Date(year, 0, 1);
    const endDate = new Date(parseInt(year) + 1, 0, 1);

    // Subscriptions growth over time
    const subscriptionsGrowth = await Subscription.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: { $month: '$createdAt' },
          newSubscriptions: { $sum: 1 },
          date: { $first: { $dateToString: { format: '%Y-%m', date: '$createdAt' } } }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Subscriptions by type
    const subscriptionsByType = await Subscription.aggregate([
      {
        $group: {
          _id: '$subscriptionType',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$price.amount' }
        }
      }
    ]);

    // Subscriptions by status
    const subscriptionsByStatus = await Subscription.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Subscriptions by service provider
    const subscriptionsByProvider = await Subscription.aggregate([
      {
        $group: {
          _id: '$serviceProvider',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$price.amount' }
        }
      }
    ]);

    // Churn analysis (cancelled subscriptions)
    const churnData = await Subscription.aggregate([
      {
        $match: {
          status: 'cancelled',
          updatedAt: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: { $month: '$updatedAt' },
          cancelledSubscriptions: { $sum: 1 },
          date: { $first: { $dateToString: { format: '%Y-%m', date: '$updatedAt' } } }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    res.json({
      period,
      year,
      subscriptionsGrowth,
      subscriptionsByType,
      subscriptionsByStatus,
      subscriptionsByProvider,
      churnData,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get subscriptions report error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/reports/customers
// @desc    Get customers report
// @access  Private (requires reports view permission)
router.get('/customers', auth, checkPermission('reports', 'view'), async (req, res) => {
  try {
    const { period = 'monthly', year = new Date().getFullYear() } = req.query;

    const startDate = new Date(year, 0, 1);
    const endDate = new Date(parseInt(year) + 1, 0, 1);

    // Customer acquisition over time
    const customerAcquisition = await Customer.aggregate([
      {
        $match: {
          registrationDate: { $gte: startDate, $lt: endDate }
        }
      },
      {
        $group: {
          _id: { $month: '$registrationDate' },
          newCustomers: { $sum: 1 },
          date: { $first: { $dateToString: { format: '%Y-%m', date: '$registrationDate' } } }
        }
      },
      {
        $sort: { '_id': 1 }
      }
    ]);

    // Customers by type
    const customersByType = await Customer.aggregate([
      {
        $group: {
          _id: '$customerType',
          count: { $sum: 1 }
        }
      }
    ]);

    // Customers by status
    const customersByStatus = await Customer.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Top customers by revenue
    const topCustomers = await Payment.aggregate([
      {
        $match: { status: 'completed' }
      },
      {
        $group: {
          _id: '$customer',
          totalRevenue: { $sum: '$amount' },
          totalPayments: { $sum: 1 }
        }
      },
      {
        $lookup: {
          from: 'customers',
          localField: '_id',
          foreignField: '_id',
          as: 'customer'
        }
      },
      {
        $unwind: '$customer'
      },
      {
        $project: {
          customerName: '$customer.name',
          customerEmail: '$customer.email',
          totalRevenue: 1,
          totalPayments: 1
        }
      },
      {
        $sort: { totalRevenue: -1 }
      },
      {
        $limit: 10
      }
    ]);

    res.json({
      period,
      year,
      customerAcquisition,
      customersByType,
      customersByStatus,
      topCustomers,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get customers report error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/reports/financial
// @desc    Get financial report
// @access  Private (requires reports view permission)
router.get('/financial', auth, checkPermission('reports', 'view'), async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), 0, 1);
    const end = endDate ? new Date(endDate) : new Date();

    // Revenue vs Expenses (for now, we'll just show revenue)
    const financialSummary = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageTransaction: { $avg: '$amount' }
        }
      }
    ]);

    // Outstanding invoices
    const outstandingInvoices = await Invoice.aggregate([
      {
        $match: {
          status: { $ne: 'paid' },
          issueDate: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: null,
          totalOutstanding: { $sum: '$totalAmount' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Monthly revenue trend
    const monthlyRevenue = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: start, $lte: end }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$paymentDate' },
            month: { $month: '$paymentDate' }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    res.json({
      startDate: start,
      endDate: end,
      financialSummary: financialSummary[0] || { totalRevenue: 0, totalTransactions: 0, averageTransaction: 0 },
      outstandingInvoices: outstandingInvoices[0] || { totalOutstanding: 0, count: 0 },
      monthlyRevenue,
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Get financial report error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
