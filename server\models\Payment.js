const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  paymentId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  invoice: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice',
    required: [true, 'الفاتورة مطلوبة']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'العميل مطلوب']
  },
  subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  amount: {
    type: Number,
    required: [true, 'مبلغ الدفع مطلوب'],
    min: [0, 'المبلغ يجب أن يكون أكبر من أو يساوي صفر'],
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  currency: {
    type: String,
    enum: ['USD', 'EUR', 'SAR', 'AED', 'EGP'],
    default: 'USD'
  },
  paymentMethod: {
    type: String,
    enum: ['credit_card', 'debit_card', 'bank_transfer', 'paypal', 'cash', 'check', 'wire_transfer'],
    required: true
  },
  paymentGateway: {
    type: String,
    enum: ['stripe', 'paypal', 'square', 'bank', 'manual'],
    default: 'manual'
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  transactionId: {
    type: String,
    trim: true
  },
  gatewayTransactionId: {
    type: String,
    trim: true
  },
  paymentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  processedDate: {
    type: Date
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'الوصف يجب أن يكون أقل من 500 حرف']
  },
  paymentDetails: {
    cardLast4: { type: String },
    cardBrand: { type: String },
    cardExpiry: { type: String },
    bankName: { type: String },
    accountLast4: { type: String },
    checkNumber: { type: String },
    paypalEmail: { type: String },
    authorizationCode: { type: String }
  },
  fees: {
    gatewayFee: {
      type: Number,
      default: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    processingFee: {
      type: Number,
      default: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    totalFees: {
      type: Number,
      default: 0,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    }
  },
  netAmount: {
    type: Number,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  refunds: [{
    refundId: { type: String, required: true },
    amount: { 
      type: Number, 
      required: true,
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    reason: { type: String, required: true },
    refundDate: { type: Date, default: Date.now },
    status: { 
      type: String, 
      enum: ['pending', 'completed', 'failed'], 
      default: 'pending' 
    },
    gatewayRefundId: { type: String },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  notes: {
    type: String,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  internalNotes: {
    type: String,
    maxlength: [1000, 'الملاحظات الداخلية يجب أن تكون أقل من 1000 حرف']
  },
  attachments: [{
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    uploadDate: { type: Date, default: Date.now }
  }],
  reconciliation: {
    isReconciled: { type: Boolean, default: false },
    reconciledDate: { type: Date },
    reconciledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    bankStatementRef: { type: String }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for better performance
paymentSchema.index({ customer: 1 });
paymentSchema.index({ invoice: 1 });
paymentSchema.index({ subscription: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ paymentDate: -1 });
paymentSchema.index({ paymentMethod: 1 });
paymentSchema.index({ paymentGateway: 1 });
paymentSchema.index({ transactionId: 1 });

// Auto-generate payment ID
paymentSchema.pre('save', async function(next) {
  if (this.isNew && !this.paymentId) {
    const currentYear = new Date().getFullYear();
    const currentMonth = String(new Date().getMonth() + 1).padStart(2, '0');
    
    // Find the last payment for this month
    const lastPayment = await this.constructor.findOne({
      paymentId: new RegExp(`^PAY-${currentYear}${currentMonth}-`)
    }).sort({ paymentId: -1 });
    
    let nextNumber = 1;
    if (lastPayment) {
      const lastNumber = parseInt(lastPayment.paymentId.split('-')[2]);
      nextNumber = lastNumber + 1;
    }
    
    this.paymentId = `PAY-${currentYear}${currentMonth}-${String(nextNumber).padStart(4, '0')}`;
  }
  next();
});

// Calculate net amount and total fees before saving
paymentSchema.pre('save', function(next) {
  // Calculate total fees
  this.fees.totalFees = this.fees.gatewayFee + this.fees.processingFee;
  
  // Calculate net amount
  this.netAmount = this.amount - this.fees.totalFees;
  
  // Round to 2 decimal places
  this.fees.totalFees = Math.round(this.fees.totalFees * 100) / 100;
  this.netAmount = Math.round(this.netAmount * 100) / 100;
  
  next();
});

// Method to process refund
paymentSchema.methods.processRefund = function(refundData) {
  const refund = {
    refundId: `REF-${Date.now()}`,
    amount: refundData.amount,
    reason: refundData.reason,
    processedBy: refundData.processedBy
  };
  
  this.refunds.push(refund);
  
  // Check if fully refunded
  const totalRefunded = this.refunds.reduce((sum, ref) => sum + ref.amount, 0);
  if (totalRefunded >= this.amount) {
    this.status = 'refunded';
  }
  
  return this.save();
};

// Virtual for total refunded amount
paymentSchema.virtual('totalRefunded').get(function() {
  return this.refunds.reduce((sum, refund) => sum + refund.amount, 0);
});

// Virtual for remaining refundable amount
paymentSchema.virtual('refundableAmount').get(function() {
  return Math.max(0, this.amount - this.totalRefunded);
});

module.exports = mongoose.model('Payment', paymentSchema);
