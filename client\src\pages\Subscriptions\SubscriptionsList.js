import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiPlus, 
  FiSearch, 
  FiFilter, 
  FiEdit, 
  FiTrash2, 
  FiEye,
  FiMoreVertical,
  FiDownload
} from 'react-icons/fi';

const PageContainer = styled.div`
  padding: var(--spacing-lg);
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
`;

const PageTitle = styled.h1`
  color: var(--color-textPrimary);
  font-size: var(--text-2xl);
  font-weight: 700;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: var(--spacing-md);
  
  @media (max-width: 768px) {
    justify-content: stretch;
  }
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &.primary {
    background-color: var(--color-primary);
    color: white;
    border: 1px solid var(--color-primary);
    
    &:hover {
      background-color: var(--color-primaryHover);
    }
  }
  
  &.secondary {
    background-color: var(--color-cardBg);
    color: var(--color-textPrimary);
    border: 1px solid var(--color-border);
    
    &:hover {
      background-color: var(--color-backgroundTertiary);
    }
  }
`;

const FiltersCard = styled.div`
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-cardBorder);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--color-cardShadow);
`;

const FiltersRow = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-md);
  align-items: end;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
`;

const Label = styled.label`
  color: var(--color-textPrimary);
  font-size: var(--text-sm);
  font-weight: 500;
`;

const Input = styled.input`
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background-color: var(--color-background);
  color: var(--color-textPrimary);
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

const Select = styled.select`
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background-color: var(--color-background);
  color: var(--color-textPrimary);
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

const TableCard = styled.div`
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-cardBorder);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--color-cardShadow);
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background-color: var(--color-backgroundTertiary);
`;

const TableRow = styled.tr`
  border-bottom: 1px solid var(--color-border);
  
  &:hover {
    background-color: var(--color-backgroundSecondary);
  }
`;

const TableCell = styled.td`
  padding: var(--spacing-md);
  text-align: right;
  color: var(--color-textPrimary);
  font-size: var(--text-sm);
`;

const TableHeaderCell = styled.th`
  padding: var(--spacing-md);
  text-align: right;
  color: var(--color-textPrimary);
  font-weight: 600;
  font-size: var(--text-sm);
`;

const StatusBadge = styled.span`
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 500;
  
  &.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
  }
  
  &.inactive {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--color-secondary);
  }
  
  &.suspended {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
  }
  
  &.cancelled {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--color-error);
  }
`;

const ActionMenu = styled.div`
  position: relative;
  display: inline-block;
`;

const ActionButton = styled.button`
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  background: none;
  border: none;
  color: var(--color-textSecondary);
  cursor: pointer;
  
  &:hover {
    background-color: var(--color-backgroundTertiary);
    color: var(--color-textPrimary);
  }
`;

const SubscriptionsList = () => {
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    type: '',
    provider: ''
  });

  // Mock data
  const mockSubscriptions = [
    {
      id: 1,
      name: 'اشتراك أساسي - شركة التقنية',
      customer: 'شركة التقنية المتقدمة',
      serviceProvider: 'AWS',
      type: 'basic',
      price: 500,
      currency: 'SAR',
      status: 'active',
      startDate: '2024-01-15',
      endDate: '2024-12-15',
      nextBilling: '2024-02-15'
    },
    {
      id: 2,
      name: 'اشتراك متقدم - مؤسسة الابتكار',
      customer: 'مؤسسة الابتكار التجاري',
      serviceProvider: 'Microsoft Azure',
      type: 'premium',
      price: 1200,
      currency: 'SAR',
      status: 'active',
      startDate: '2024-01-10',
      endDate: '2024-12-10',
      nextBilling: '2024-02-10'
    },
    {
      id: 3,
      name: 'اشتراك مؤسسي - الشركة الكبرى',
      customer: 'الشركة الكبرى للتجارة',
      serviceProvider: 'Google Cloud',
      type: 'enterprise',
      price: 2500,
      currency: 'SAR',
      status: 'suspended',
      startDate: '2023-12-01',
      endDate: '2024-11-30',
      nextBilling: '2024-02-01'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchSubscriptions = async () => {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSubscriptions(mockSubscriptions);
      setLoading(false);
    };

    fetchSubscriptions();
  }, []);

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getStatusLabel = (status) => {
    const labels = {
      active: 'نشط',
      inactive: 'غير نشط',
      suspended: 'معلق',
      cancelled: 'ملغي',
      expired: 'منتهي'
    };
    return labels[status] || status;
  };

  const getTypeLabel = (type) => {
    const labels = {
      basic: 'أساسي',
      premium: 'متقدم',
      enterprise: 'مؤسسي',
      custom: 'مخصص'
    };
    return labels[type] || type;
  };

  const formatCurrency = (amount, currency) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: currency || 'SAR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-SA');
  };

  if (loading) {
    return (
      <PageContainer>
        <div>جاري تحميل الاشتراكات...</div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <PageHeader>
        <PageTitle>إدارة الاشتراكات</PageTitle>
        <ActionButtons>
          <Button className="secondary">
            <FiDownload />
            تصدير
          </Button>
          <Button as={Link} to="/subscriptions/new" className="primary">
            <FiPlus />
            اشتراك جديد
          </Button>
        </ActionButtons>
      </PageHeader>

      <FiltersCard>
        <FiltersRow>
          <FormGroup>
            <Label>البحث</Label>
            <Input
              type="text"
              placeholder="البحث في الاشتراكات..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </FormGroup>
          
          <FormGroup>
            <Label>الحالة</Label>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">جميع الحالات</option>
              <option value="active">نشط</option>
              <option value="inactive">غير نشط</option>
              <option value="suspended">معلق</option>
              <option value="cancelled">ملغي</option>
            </Select>
          </FormGroup>
          
          <FormGroup>
            <Label>النوع</Label>
            <Select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
            >
              <option value="">جميع الأنواع</option>
              <option value="basic">أساسي</option>
              <option value="premium">متقدم</option>
              <option value="enterprise">مؤسسي</option>
              <option value="custom">مخصص</option>
            </Select>
          </FormGroup>
          
          <FormGroup>
            <Label>مزود الخدمة</Label>
            <Select
              value={filters.provider}
              onChange={(e) => handleFilterChange('provider', e.target.value)}
            >
              <option value="">جميع المزودين</option>
              <option value="AWS">AWS</option>
              <option value="Microsoft Azure">Microsoft Azure</option>
              <option value="Google Cloud">Google Cloud</option>
            </Select>
          </FormGroup>
        </FiltersRow>
      </FiltersCard>

      <TableCard>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHeaderCell>اسم الاشتراك</TableHeaderCell>
              <TableHeaderCell>العميل</TableHeaderCell>
              <TableHeaderCell>مزود الخدمة</TableHeaderCell>
              <TableHeaderCell>النوع</TableHeaderCell>
              <TableHeaderCell>السعر</TableHeaderCell>
              <TableHeaderCell>الحالة</TableHeaderCell>
              <TableHeaderCell>التجديد التالي</TableHeaderCell>
              <TableHeaderCell>الإجراءات</TableHeaderCell>
            </TableRow>
          </TableHeader>
          <tbody>
            {subscriptions.map((subscription) => (
              <TableRow key={subscription.id}>
                <TableCell>
                  <Link 
                    to={`/subscriptions/${subscription.id}`}
                    style={{ color: 'var(--color-primary)', fontWeight: '500' }}
                  >
                    {subscription.name}
                  </Link>
                </TableCell>
                <TableCell>{subscription.customer}</TableCell>
                <TableCell>{subscription.serviceProvider}</TableCell>
                <TableCell>{getTypeLabel(subscription.type)}</TableCell>
                <TableCell>{formatCurrency(subscription.price, subscription.currency)}</TableCell>
                <TableCell>
                  <StatusBadge className={subscription.status}>
                    {getStatusLabel(subscription.status)}
                  </StatusBadge>
                </TableCell>
                <TableCell>{formatDate(subscription.nextBilling)}</TableCell>
                <TableCell>
                  <ActionMenu>
                    <ActionButton>
                      <FiMoreVertical />
                    </ActionButton>
                  </ActionMenu>
                </TableCell>
              </TableRow>
            ))}
          </tbody>
        </Table>
      </TableCard>
    </PageContainer>
  );
};

export default SubscriptionsList;
