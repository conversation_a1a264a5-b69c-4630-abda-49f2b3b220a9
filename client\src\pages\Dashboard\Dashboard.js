import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { 
  FiUsers, 
  FiCreditCard, 
  FiDollarSign, 
  FiFileText,
  FiTrendingUp,
  FiTrendingDown,
  FiAlertCircle,
  FiCheckCircle
} from 'react-icons/fi';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { useAuth } from '../../contexts/AuthContext';

const DashboardContainer = styled.div`
  padding: var(--spacing-lg);
  max-width: 1400px;
  margin: 0 auto;
`;

const PageHeader = styled.div`
  margin-bottom: var(--spacing-xl);
`;

const PageTitle = styled.h1`
  color: var(--color-textPrimary);
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
`;

const PageSubtitle = styled.p`
  color: var(--color-textSecondary);
  font-size: var(--text-base);
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
`;

const StatCard = styled.div`
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-cardBorder);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--color-cardShadow);
  transition: all 0.2s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
`;

const StatHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
`;

const StatIcon = styled.div`
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  background-color: ${props => props.bgColor || 'var(--color-primary)'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
`;

const StatTrend = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: ${props => props.isPositive ? 'var(--color-success)' : 'var(--color-error)'};
  font-size: var(--text-sm);
  font-weight: 500;
`;

const StatValue = styled.div`
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--color-textPrimary);
  margin-bottom: var(--spacing-xs);
`;

const StatLabel = styled.div`
  color: var(--color-textSecondary);
  font-size: var(--text-sm);
`;

const ChartsGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const ChartCard = styled.div`
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-cardBorder);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--color-cardShadow);
`;

const ChartTitle = styled.h3`
  color: var(--color-textPrimary);
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
`;

const RecentActivity = styled.div`
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-cardBorder);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--color-cardShadow);
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--color-border);
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background-color: ${props => props.bgColor || 'var(--color-primary)'};
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-sm);
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityText = styled.div`
  color: var(--color-textPrimary);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-xs);
`;

const ActivityTime = styled.div`
  color: var(--color-textMuted);
  font-size: var(--text-xs);
`;

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    totalCustomers: 0,
    monthlyRevenue: 0,
    pendingInvoices: 0,
    overdueInvoices: 0
  });
  const [loading, setLoading] = useState(true);

  // Mock data for charts
  const revenueData = [
    { month: 'يناير', revenue: 12000, subscriptions: 45 },
    { month: 'فبراير', revenue: 15000, subscriptions: 52 },
    { month: 'مارس', revenue: 18000, subscriptions: 61 },
    { month: 'أبريل', revenue: 22000, subscriptions: 68 },
    { month: 'مايو', revenue: 25000, subscriptions: 75 },
    { month: 'يونيو', revenue: 28000, subscriptions: 82 }
  ];

  const subscriptionTypeData = [
    { name: 'أساسي', value: 45, color: '#3b82f6' },
    { name: 'متقدم', value: 30, color: '#10b981' },
    { name: 'مؤسسي', value: 20, color: '#f59e0b' },
    { name: 'مخصص', value: 5, color: '#ef4444' }
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'subscription',
      text: 'اشتراك جديد من شركة التقنية المتقدمة',
      time: 'منذ 5 دقائق',
      icon: FiCreditCard,
      bgColor: '#10b981'
    },
    {
      id: 2,
      type: 'payment',
      text: 'تم استلام دفعة بقيمة 5,000 ريال',
      time: 'منذ 15 دقيقة',
      icon: FiDollarSign,
      bgColor: '#3b82f6'
    },
    {
      id: 3,
      type: 'invoice',
      text: 'فاتورة جديدة تم إرسالها للعميل أحمد محمد',
      time: 'منذ 30 دقيقة',
      icon: FiFileText,
      bgColor: '#f59e0b'
    },
    {
      id: 4,
      type: 'customer',
      text: 'عميل جديد: مؤسسة الابتكار التجاري',
      time: 'منذ ساعة',
      icon: FiUsers,
      bgColor: '#8b5cf6'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchDashboardData = async () => {
      setLoading(true);
      // Mock API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        totalSubscriptions: 156,
        activeSubscriptions: 142,
        totalCustomers: 89,
        monthlyRevenue: 28500,
        pendingInvoices: 12,
        overdueInvoices: 3
      });
      
      setLoading(false);
    };

    fetchDashboardData();
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  if (loading) {
    return (
      <DashboardContainer>
        <div>جاري تحميل البيانات...</div>
      </DashboardContainer>
    );
  }

  return (
    <DashboardContainer>
      <PageHeader>
        <PageTitle>مرحباً، {user?.name}</PageTitle>
        <PageSubtitle>إليك نظرة عامة على أداء النظام اليوم</PageSubtitle>
      </PageHeader>

      <StatsGrid>
        <StatCard>
          <StatHeader>
            <StatIcon bgColor="#3b82f6">
              <FiCreditCard />
            </StatIcon>
            <StatTrend isPositive={true}>
              <FiTrendingUp />
              +12%
            </StatTrend>
          </StatHeader>
          <StatValue>{stats.totalSubscriptions}</StatValue>
          <StatLabel>إجمالي الاشتراكات</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon bgColor="#10b981">
              <FiCheckCircle />
            </StatIcon>
            <StatTrend isPositive={true}>
              <FiTrendingUp />
              +8%
            </StatTrend>
          </StatHeader>
          <StatValue>{stats.activeSubscriptions}</StatValue>
          <StatLabel>الاشتراكات النشطة</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon bgColor="#8b5cf6">
              <FiUsers />
            </StatIcon>
            <StatTrend isPositive={true}>
              <FiTrendingUp />
              +15%
            </StatTrend>
          </StatHeader>
          <StatValue>{stats.totalCustomers}</StatValue>
          <StatLabel>إجمالي العملاء</StatLabel>
        </StatCard>

        <StatCard>
          <StatHeader>
            <StatIcon bgColor="#f59e0b">
              <FiDollarSign />
            </StatIcon>
            <StatTrend isPositive={true}>
              <FiTrendingUp />
              +22%
            </StatTrend>
          </StatHeader>
          <StatValue>{formatCurrency(stats.monthlyRevenue)}</StatValue>
          <StatLabel>الإيرادات الشهرية</StatLabel>
        </StatCard>
      </StatsGrid>

      <ChartsGrid>
        <ChartCard>
          <ChartTitle>نمو الإيرادات والاشتراكات</ChartTitle>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="revenue" 
                stroke="#3b82f6" 
                strokeWidth={2}
                name="الإيرادات"
              />
              <Line 
                type="monotone" 
                dataKey="subscriptions" 
                stroke="#10b981" 
                strokeWidth={2}
                name="الاشتراكات"
              />
            </LineChart>
          </ResponsiveContainer>
        </ChartCard>

        <ChartCard>
          <ChartTitle>توزيع أنواع الاشتراكات</ChartTitle>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={subscriptionTypeData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {subscriptionTypeData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </ChartCard>
      </ChartsGrid>

      <RecentActivity>
        <ChartTitle>النشاط الأخير</ChartTitle>
        {recentActivities.map((activity) => (
          <ActivityItem key={activity.id}>
            <ActivityIcon bgColor={activity.bgColor}>
              <activity.icon />
            </ActivityIcon>
            <ActivityContent>
              <ActivityText>{activity.text}</ActivityText>
              <ActivityTime>{activity.time}</ActivityTime>
            </ActivityContent>
          </ActivityItem>
        ))}
      </RecentActivity>
    </DashboardContainer>
  );
};

export default Dashboard;
