{"name": "subscription-management-client", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "recharts": "^2.8.0", "react-icons": "^4.12.0", "styled-components": "^6.1.1", "react-datepicker": "^4.24.0", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-hook-form": "^7.48.2", "react-toastify": "^9.1.3", "moment": "^2.29.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}