import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiHome, 
  FiUsers, 
  FiCreditCard, 
  FiFileText, 
  FiDollarSign,
  FiMail,
  FiBarChart3,
  FiSettings,
  FiUser,
  FiChevronLeft,
  FiChevronRight,
  FiMenu,
  FiX
} from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const SidebarContainer = styled.aside`
  position: fixed;
  top: 0;
  right: ${props => props.isRTL ? '0' : 'auto'};
  left: ${props => props.isRTL ? 'auto' : '0'};
  height: 100vh;
  width: ${props => props.collapsed ? '80px' : '280px'};
  background-color: var(--color-sidebarBg);
  transition: width 0.3s ease;
  z-index: 1000;
  overflow-y: auto;
  overflow-x: hidden;
  
  @media (max-width: 768px) {
    width: 280px;
    transform: translateX(${props => {
      if (props.isRTL) {
        return props.mobileOpen ? '0' : '100%';
      } else {
        return props.mobileOpen ? '0' : '-100%';
      }
    }});
  }
`;

const SidebarHeader = styled.div`
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--color-sidebarTextActive);
  font-size: var(--text-lg);
  font-weight: 600;
  
  ${props => props.collapsed && `
    justify-content: center;
    
    span {
      display: none;
    }
  `}
`;

const ToggleButton = styled.button`
  background: none;
  border: none;
  color: var(--color-sidebarText);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--color-sidebarHover);
    color: var(--color-sidebarTextActive);
  }
  
  @media (max-width: 768px) {
    display: ${props => props.collapsed ? 'none' : 'block'};
  }
`;

const MobileCloseButton = styled.button`
  display: none;
  background: none;
  border: none;
  color: var(--color-sidebarText);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  
  @media (max-width: 768px) {
    display: block;
  }
  
  &:hover {
    background-color: var(--color-sidebarHover);
    color: var(--color-sidebarTextActive);
  }
`;

const Navigation = styled.nav`
  padding: var(--spacing-lg) 0;
`;

const NavSection = styled.div`
  margin-bottom: var(--spacing-xl);
`;

const NavSectionTitle = styled.h3`
  color: var(--color-sidebarText);
  font-size: var(--text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  
  ${props => props.collapsed && `
    display: none;
  `}
`;

const NavItem = styled(NavLink)`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--color-sidebarText);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background-color: var(--color-sidebarHover);
    color: var(--color-sidebarTextActive);
  }
  
  &.active {
    background-color: var(--color-primary);
    color: var(--color-textInverse);
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: ${props => props.isRTL ? '0' : 'auto'};
      left: ${props => props.isRTL ? 'auto' : '0'};
      width: 4px;
      height: 100%;
      background-color: var(--color-textInverse);
    }
  }
  
  ${props => props.collapsed && `
    justify-content: center;
    
    span {
      display: none;
    }
  `}
`;

const NavIcon = styled.div`
  font-size: var(--text-lg);
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Overlay = styled.div`
  display: none;
  
  @media (max-width: 768px) {
    display: ${props => props.show ? 'block' : 'none'};
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
`;

const Sidebar = ({ collapsed, mobileOpen, onToggle, onMobileToggle }) => {
  const { hasPermission } = useAuth();
  const { isRTL } = useTheme();

  const navigationItems = [
    {
      section: 'الرئيسية',
      items: [
        {
          path: '/',
          icon: FiHome,
          label: 'لوحة المعلومات',
          permission: 'dashboard'
        }
      ]
    },
    {
      section: 'إدارة الاشتراكات',
      items: [
        {
          path: '/subscriptions',
          icon: FiCreditCard,
          label: 'قائمة الاشتراكات',
          permission: { resource: 'subscriptions', action: 'view' }
        },
        {
          path: '/subscriptions/analytics',
          icon: FiBarChart3,
          label: 'تحليلات الاشتراكات',
          permission: { resource: 'subscriptions', action: 'view' }
        },
        {
          path: '/customers',
          icon: FiUsers,
          label: 'العملاء',
          permission: { resource: 'customers', action: 'view' }
        }
      ]
    },
    {
      section: 'إدارة الفواتير',
      items: [
        {
          path: '/invoices',
          icon: FiFileText,
          label: 'قائمة الفواتير',
          permission: { resource: 'invoices', action: 'view' }
        },
        {
          path: '/payments',
          icon: FiDollarSign,
          label: 'المدفوعات',
          permission: { resource: 'invoices', action: 'view' }
        }
      ]
    },
    {
      section: 'التواصل والتقارير',
      items: [
        {
          path: '/notifications',
          icon: FiMail,
          label: 'مركز التواصل',
          permission: { resource: 'notifications', action: 'view' }
        },
        {
          path: '/reports',
          icon: FiBarChart3,
          label: 'التقارير العامة',
          permission: { resource: 'reports', action: 'view' }
        }
      ]
    },
    {
      section: 'الإدارة',
      items: [
        {
          path: '/users',
          icon: FiUser,
          label: 'إدارة المستخدمين',
          permission: { resource: 'users', action: 'view' }
        },
        {
          path: '/settings',
          icon: FiSettings,
          label: 'الإعدادات المتقدمة',
          permission: { resource: 'settings', action: 'view' }
        }
      ]
    }
  ];

  const checkPermission = (permission) => {
    if (typeof permission === 'string') {
      return hasPermission(permission, 'view');
    }
    if (typeof permission === 'object') {
      return hasPermission(permission.resource, permission.action);
    }
    return true;
  };

  return (
    <>
      <Overlay show={mobileOpen} onClick={onMobileToggle} />
      
      <SidebarContainer 
        collapsed={collapsed} 
        mobileOpen={mobileOpen}
        isRTL={isRTL}
      >
        <SidebarHeader>
          <Logo collapsed={collapsed}>
            <FiCreditCard />
            <span>نظام الاشتراكات</span>
          </Logo>
          
          <ToggleButton 
            onClick={onToggle}
            collapsed={collapsed}
          >
            {isRTL ? 
              (collapsed ? <FiChevronLeft /> : <FiChevronRight />) :
              (collapsed ? <FiChevronRight /> : <FiChevronLeft />)
            }
          </ToggleButton>
          
          <MobileCloseButton onClick={onMobileToggle}>
            <FiX />
          </MobileCloseButton>
        </SidebarHeader>

        <Navigation>
          {navigationItems.map((section, sectionIndex) => (
            <NavSection key={sectionIndex}>
              <NavSectionTitle collapsed={collapsed}>
                {section.section}
              </NavSectionTitle>
              
              {section.items.map((item, itemIndex) => {
                if (!checkPermission(item.permission)) {
                  return null;
                }
                
                return (
                  <NavItem
                    key={itemIndex}
                    to={item.path}
                    collapsed={collapsed}
                    isRTL={isRTL}
                    className={({ isActive }) => isActive ? 'active' : ''}
                    onClick={() => window.innerWidth <= 768 && onMobileToggle()}
                  >
                    <NavIcon>
                      <item.icon />
                    </NavIcon>
                    <span>{item.label}</span>
                  </NavItem>
                );
              })}
            </NavSection>
          ))}
        </Navigation>
      </SidebarContainer>
    </>
  );
};

export default Sidebar;
