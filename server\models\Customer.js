const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم العميل مطلوب'],
    trim: true,
    maxlength: [100, 'اسم العميل يجب أن يكون أقل من 100 حرف']
  },
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'رقم الهاتف غير صحيح']
  },
  company: {
    type: String,
    trim: true,
    maxlength: [100, 'اسم الشركة يجب أن يكون أقل من 100 حرف']
  },
  address: {
    street: { type: String, trim: true },
    city: { type: String, trim: true },
    state: { type: String, trim: true },
    country: { type: String, trim: true },
    zipCode: { type: String, trim: true }
  },
  contactPerson: {
    name: { type: String, trim: true },
    email: { type: String, lowercase: true },
    phone: { type: String, trim: true },
    position: { type: String, trim: true }
  },
  customerType: {
    type: String,
    enum: ['individual', 'business', 'enterprise'],
    default: 'individual'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'pending'],
    default: 'active'
  },
  paymentMethod: {
    type: {
      type: String,
      enum: ['credit_card', 'bank_transfer', 'paypal', 'cash', 'check'],
      default: 'credit_card'
    },
    details: {
      cardNumber: { type: String, select: false }, // Encrypted
      cardHolderName: { type: String },
      expiryDate: { type: String, select: false }, // Encrypted
      bankName: { type: String },
      accountNumber: { type: String, select: false }, // Encrypted
      routingNumber: { type: String, select: false }, // Encrypted
      paypalEmail: { type: String }
    }
  },
  billingCycle: {
    type: String,
    enum: ['monthly', 'quarterly', 'semi-annual', 'annual'],
    default: 'monthly'
  },
  currency: {
    type: String,
    enum: ['USD', 'EUR', 'SAR', 'AED', 'EGP'],
    default: 'USD'
  },
  taxId: {
    type: String,
    trim: true
  },
  notes: {
    type: String,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  tags: [{
    type: String,
    trim: true
  }],
  accountBalance: {
    type: Number,
    default: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  creditLimit: {
    type: Number,
    default: 0,
    get: v => Math.round(v * 100) / 100,
    set: v => Math.round(v * 100) / 100
  },
  lastPaymentDate: {
    type: Date
  },
  registrationDate: {
    type: Date,
    default: Date.now
  },
  source: {
    type: String,
    enum: ['website', 'referral', 'social_media', 'advertisement', 'direct', 'other'],
    default: 'direct'
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for better performance
customerSchema.index({ email: 1 });
customerSchema.index({ status: 1 });
customerSchema.index({ customerType: 1 });
customerSchema.index({ assignedTo: 1 });
customerSchema.index({ registrationDate: -1 });

// Virtual for full name
customerSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  if (!addr) return '';
  
  const parts = [addr.street, addr.city, addr.state, addr.country, addr.zipCode]
    .filter(part => part && part.trim());
  return parts.join(', ');
});

// Virtual for subscription count
customerSchema.virtual('subscriptionCount', {
  ref: 'Subscription',
  localField: '_id',
  foreignField: 'customer',
  count: true
});

// Virtual for total revenue
customerSchema.virtual('totalRevenue', {
  ref: 'Invoice',
  localField: '_id',
  foreignField: 'customer',
  match: { status: 'paid' },
  options: { sort: { createdAt: -1 } }
});

module.exports = mongoose.model('Customer', customerSchema);
