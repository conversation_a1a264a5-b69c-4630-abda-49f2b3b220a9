const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify JWT token
const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد رمز مميز، الوصول مرفوض' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.id).select('-password');
    
    if (!user) {
      return res.status(401).json({ message: 'الرمز المميز غير صحيح' });
    }

    if (!user.isActive) {
      return res.status(401).json({ message: 'الحساب غير نشط' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(401).json({ message: 'الرمز المميز غير صحيح' });
  }
};

// Middleware to check specific permissions
const checkPermission = (resource, action) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({ message: 'المستخدم غير مصرح له' });
      }

      // Admin has all permissions
      if (user.role === 'admin') {
        return next();
      }

      // Check specific permission
      const hasPermission = user.permissions[resource] && user.permissions[resource][action];
      
      if (!hasPermission) {
        return res.status(403).json({ 
          message: `ليس لديك صلاحية ${action} في ${resource}` 
        });
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ message: 'خطأ في التحقق من الصلاحيات' });
    }
  };
};

// Middleware to check if user is admin
const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: 'يتطلب صلاحيات المدير' });
  }
};

// Middleware to check if user is manager or admin
const isManagerOrAdmin = (req, res, next) => {
  if (req.user && (req.user.role === 'admin' || req.user.role === 'manager')) {
    next();
  } else {
    res.status(403).json({ message: 'يتطلب صلاحيات المدير أو المشرف' });
  }
};

// Middleware to check multiple roles
const hasRole = (roles) => {
  return (req, res, next) => {
    if (req.user && roles.includes(req.user.role)) {
      next();
    } else {
      res.status(403).json({ 
        message: `يتطلب أحد الأدوار التالية: ${roles.join(', ')}` 
      });
    }
  };
};

module.exports = {
  auth,
  checkPermission,
  isAdmin,
  isManagerOrAdmin,
  hasRole
};
