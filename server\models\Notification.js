const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'عنوان الإشعار مطلوب'],
    trim: true,
    maxlength: [200, 'العنوان يجب أن يكون أقل من 200 حرف']
  },
  message: {
    type: String,
    required: [true, 'نص الإشعار مطلوب'],
    trim: true,
    maxlength: [1000, 'النص يجب أن يكون أقل من 1000 حرف']
  },
  type: {
    type: String,
    enum: ['email', 'sms', 'push', 'system'],
    required: true
  },
  category: {
    type: String,
    enum: ['subscription_renewal', 'payment_due', 'payment_received', 'subscription_expired', 'system_alert', 'custom'],
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  recipients: [{
    recipientType: {
      type: String,
      enum: ['customer', 'user', 'email'],
      required: true
    },
    recipientId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'recipients.recipientType'
    },
    email: {
      type: String,
      lowercase: true
    },
    phone: {
      type: String
    },
    name: {
      type: String
    },
    status: {
      type: String,
      enum: ['pending', 'sent', 'delivered', 'failed', 'bounced'],
      default: 'pending'
    },
    sentAt: {
      type: Date
    },
    deliveredAt: {
      type: Date
    },
    errorMessage: {
      type: String
    },
    attempts: {
      type: Number,
      default: 0
    }
  }],
  template: {
    templateId: {
      type: String
    },
    variables: {
      type: Map,
      of: String
    }
  },
  scheduledFor: {
    type: Date
  },
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sending', 'sent', 'failed', 'cancelled'],
    default: 'draft'
  },
  relatedTo: {
    entityType: {
      type: String,
      enum: ['subscription', 'invoice', 'payment', 'customer', 'user'],
    },
    entityId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'relatedTo.entityType'
    }
  },
  attachments: [{
    filename: { type: String, required: true },
    originalName: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    uploadDate: { type: Date, default: Date.now }
  }],
  deliverySettings: {
    maxRetries: {
      type: Number,
      default: 3,
      min: 0,
      max: 10
    },
    retryInterval: {
      type: Number,
      default: 60, // minutes
      min: 1
    },
    expiresAt: {
      type: Date
    }
  },
  analytics: {
    opened: {
      count: { type: Number, default: 0 },
      firstOpenedAt: { type: Date },
      lastOpenedAt: { type: Date }
    },
    clicked: {
      count: { type: Number, default: 0 },
      firstClickedAt: { type: Date },
      lastClickedAt: { type: Date },
      links: [{
        url: { type: String },
        clicks: { type: Number, default: 0 }
      }]
    }
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
notificationSchema.index({ status: 1 });
notificationSchema.index({ type: 1 });
notificationSchema.index({ category: 1 });
notificationSchema.index({ scheduledFor: 1 });
notificationSchema.index({ 'relatedTo.entityType': 1, 'relatedTo.entityId': 1 });
notificationSchema.index({ createdAt: -1 });

// Method to send notification
notificationSchema.methods.send = async function() {
  this.status = 'sending';
  await this.save();
  
  // Here you would implement the actual sending logic
  // This is a placeholder for the notification service
  try {
    // Send to each recipient
    for (let recipient of this.recipients) {
      recipient.attempts += 1;
      recipient.sentAt = new Date();
      
      // Simulate sending (replace with actual implementation)
      const success = await this.sendToRecipient(recipient);
      
      if (success) {
        recipient.status = 'sent';
        recipient.deliveredAt = new Date();
      } else {
        recipient.status = 'failed';
        recipient.errorMessage = 'Failed to send notification';
      }
    }
    
    // Update overall status
    const allSent = this.recipients.every(r => r.status === 'sent');
    const anyFailed = this.recipients.some(r => r.status === 'failed');
    
    if (allSent) {
      this.status = 'sent';
    } else if (anyFailed) {
      this.status = 'failed';
    }
    
    await this.save();
    return true;
  } catch (error) {
    this.status = 'failed';
    await this.save();
    throw error;
  }
};

// Placeholder method for sending to individual recipient
notificationSchema.methods.sendToRecipient = async function(recipient) {
  // This would be implemented based on the notification type
  // For now, just return true to simulate success
  return true;
};

// Method to retry failed notifications
notificationSchema.methods.retry = async function() {
  const failedRecipients = this.recipients.filter(r => 
    r.status === 'failed' && r.attempts < this.deliverySettings.maxRetries
  );
  
  if (failedRecipients.length === 0) {
    return false;
  }
  
  for (let recipient of failedRecipients) {
    const success = await this.sendToRecipient(recipient);
    recipient.attempts += 1;
    recipient.sentAt = new Date();
    
    if (success) {
      recipient.status = 'sent';
      recipient.deliveredAt = new Date();
      recipient.errorMessage = undefined;
    } else {
      recipient.errorMessage = 'Retry failed';
    }
  }
  
  await this.save();
  return true;
};

// Virtual for delivery rate
notificationSchema.virtual('deliveryRate').get(function() {
  if (this.recipients.length === 0) return 0;
  const delivered = this.recipients.filter(r => r.status === 'sent' || r.status === 'delivered').length;
  return Math.round((delivered / this.recipients.length) * 100);
});

module.exports = mongoose.model('Notification', notificationSchema);
