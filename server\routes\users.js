const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { auth, checkPermission, isAdmin } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/users
// @desc    Get all users
// @access  Private (requires users view permission)
router.get('/', auth, checkPermission('users', 'view'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search, role, status } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    if (status !== undefined) {
      query.isActive = status === 'active';
    }

    // Execute query with pagination
    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await User.countDocuments(query);

    res.json({
      users,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/users/:id
// @desc    Get user by ID
// @access  Private (requires users view permission)
router.get('/:id', auth, checkPermission('users', 'view'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    res.json({ user });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/users
// @desc    Create new user
// @access  Private (requires users create permission)
router.post('/', auth, checkPermission('users', 'create'), [
  body('name').trim().isLength({ min: 2 }).withMessage('الاسم يجب أن يكون على الأقل حرفين'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('password').isLength({ min: 6 }).withMessage('كلمة المرور يجب أن تكون على الأقل 6 أحرف'),
  body('role').isIn(['admin', 'manager', 'employee', 'viewer']).withMessage('الدور غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { name, email, password, role, phone, department } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'المستخدم موجود بالفعل' });
    }

    // Create new user
    const user = new User({
      name,
      email,
      password,
      role,
      phone,
      department
    });

    await user.save();

    res.status(201).json({
      message: 'تم إنشاء المستخدم بنجاح',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone,
        department: user.department,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/users/:id
// @desc    Update user
// @access  Private (requires users edit permission)
router.put('/:id', auth, checkPermission('users', 'edit'), [
  body('name').optional().trim().isLength({ min: 2 }).withMessage('الاسم يجب أن يكون على الأقل حرفين'),
  body('email').optional().isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('role').optional().isIn(['admin', 'manager', 'employee', 'viewer']).withMessage('الدور غير صحيح'),
  body('phone').optional().isMobilePhone().withMessage('رقم الهاتف غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const { name, email, role, phone, department, isActive } = req.body;
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // Check if email is already taken by another user
    if (email && email !== user.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({ message: 'البريد الإلكتروني مستخدم بالفعل' });
      }
    }

    // Update user fields
    if (name) user.name = name;
    if (email) user.email = email;
    if (role) user.role = role;
    if (phone) user.phone = phone;
    if (department) user.department = department;
    if (isActive !== undefined) user.isActive = isActive;

    await user.save();

    res.json({
      message: 'تم تحديث المستخدم بنجاح',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        phone: user.phone,
        department: user.department,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   DELETE /api/users/:id
// @desc    Delete user
// @access  Private (requires users delete permission)
router.delete('/:id', auth, checkPermission('users', 'delete'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // Prevent deleting yourself
    if (user._id.toString() === req.user.id) {
      return res.status(400).json({ message: 'لا يمكنك حذف حسابك الخاص' });
    }

    await User.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/users/:id/toggle-status
// @desc    Toggle user active status
// @access  Private (requires users edit permission)
router.put('/:id/toggle-status', auth, checkPermission('users', 'edit'), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // Prevent deactivating yourself
    if (user._id.toString() === req.user.id) {
      return res.status(400).json({ message: 'لا يمكنك إلغاء تفعيل حسابك الخاص' });
    }

    user.isActive = !user.isActive;
    await user.save();

    res.json({
      message: `تم ${user.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم بنجاح`,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        isActive: user.isActive
      }
    });
  } catch (error) {
    console.error('Toggle user status error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
