import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import styled from 'styled-components';
import { FiMail, FiLock, <PERSON>Eye, FiEyeOff, FiCreditCard } from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primaryHover) 100%);
  padding: var(--spacing-lg);
`;

const LoginCard = styled.div`
  background-color: var(--color-cardBg);
  border-radius: var(--radius-xl);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: var(--spacing-xxl);
  width: 100%;
  max-width: 400px;
  direction: ${props => props.isRTL ? 'rtl' : 'ltr'};
`;

const LoginHeader = styled.div`
  text-align: center;
  margin-bottom: var(--spacing-xxl);
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary);
  font-size: var(--text-2xl);
`;

const Title = styled.h1`
  color: var(--color-textPrimary);
  font-size: var(--text-2xl);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
`;

const Subtitle = styled.p`
  color: var(--color-textSecondary);
  font-size: var(--text-base);
`;

const LoginForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
`;

const Label = styled.label`
  color: var(--color-textPrimary);
  font-size: var(--text-sm);
  font-weight: 500;
`;

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const Input = styled.input`
  width: 100%;
  padding: var(--spacing-md);
  padding-right: ${props => props.hasIcon && props.isRTL ? '40px' : 'var(--spacing-md)'};
  padding-left: ${props => props.hasIcon && !props.isRTL ? '40px' : 'var(--spacing-md)'};
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-size: var(--text-base);
  background-color: var(--color-background);
  color: var(--color-textPrimary);
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
  
  &::placeholder {
    color: var(--color-textMuted);
  }
`;

const InputIcon = styled.div`
  position: absolute;
  right: ${props => props.isRTL ? 'var(--spacing-md)' : 'auto'};
  left: ${props => props.isRTL ? 'auto' : 'var(--spacing-md)'};
  color: var(--color-textMuted);
  pointer-events: none;
`;

const PasswordToggle = styled.button`
  position: absolute;
  left: ${props => props.isRTL ? 'auto' : 'var(--spacing-md)'};
  right: ${props => props.isRTL ? 'var(--spacing-md)' : 'auto'};
  background: none;
  border: none;
  color: var(--color-textMuted);
  cursor: pointer;
  padding: var(--spacing-xs);
  
  &:hover {
    color: var(--color-textSecondary);
  }
`;

const SubmitButton = styled.button`
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  font-size: var(--text-base);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background-color: var(--color-primaryHover);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: var(--color-error);
  font-size: var(--text-sm);
  text-align: center;
  padding: var(--spacing-sm);
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: var(--radius-md);
  border: 1px solid rgba(239, 68, 68, 0.2);
`;

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login, isAuthenticated, error: authError } = useAuth();
  const { isRTL } = useTheme();

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Handle form input changes
  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear error when user starts typing
    if (error || authError) {
      setError('');
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Basic validation
    if (!formData.email || !formData.password) {
      setError('يرجى ملء جميع الحقول');
      setLoading(false);
      return;
    }

    try {
      const result = await login(formData.email, formData.password);
      if (!result.success) {
        setError(result.error);
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LoginContainer>
      <LoginCard isRTL={isRTL}>
        <LoginHeader>
          <Logo>
            <FiCreditCard />
            <span>نظام الاشتراكات</span>
          </Logo>
          <Title>تسجيل الدخول</Title>
          <Subtitle>أدخل بياناتك للوصول إلى النظام</Subtitle>
        </LoginHeader>

        <LoginForm onSubmit={handleSubmit}>
          <FormGroup>
            <Label htmlFor="email">البريد الإلكتروني</Label>
            <InputWrapper>
              <Input
                type="email"
                id="email"
                name="email"
                placeholder="أدخل بريدك الإلكتروني"
                value={formData.email}
                onChange={handleChange}
                hasIcon
                isRTL={isRTL}
                required
              />
              <InputIcon isRTL={isRTL}>
                <FiMail />
              </InputIcon>
            </InputWrapper>
          </FormGroup>

          <FormGroup>
            <Label htmlFor="password">كلمة المرور</Label>
            <InputWrapper>
              <Input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                placeholder="أدخل كلمة المرور"
                value={formData.password}
                onChange={handleChange}
                hasIcon
                isRTL={isRTL}
                required
              />
              <InputIcon isRTL={isRTL}>
                <FiLock />
              </InputIcon>
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                isRTL={isRTL}
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </PasswordToggle>
            </InputWrapper>
          </FormGroup>

          {(error || authError) && (
            <ErrorMessage>
              {error || authError}
            </ErrorMessage>
          )}

          <SubmitButton type="submit" disabled={loading}>
            {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
          </SubmitButton>
        </LoginForm>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
