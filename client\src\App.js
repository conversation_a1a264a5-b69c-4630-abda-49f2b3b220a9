import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Context
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Components
import Layout from './components/Layout/Layout';
import Login from './components/Auth/Login';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Pages
import Dashboard from './pages/Dashboard/Dashboard';
import SubscriptionsList from './pages/Subscriptions/SubscriptionsList';
import SubscriptionForm from './pages/Subscriptions/SubscriptionForm';
import SubscriptionDetails from './pages/Subscriptions/SubscriptionDetails';
import SubscriptionAnalytics from './pages/Subscriptions/SubscriptionAnalytics';
import CustomersList from './pages/Customers/CustomersList';
import CustomerForm from './pages/Customers/CustomerForm';
import CustomerDetails from './pages/Customers/CustomerDetails';
import InvoicesList from './pages/Invoices/InvoicesList';
import InvoiceForm from './pages/Invoices/InvoiceForm';
import InvoiceDetails from './pages/Invoices/InvoiceDetails';
import PaymentsList from './pages/Payments/PaymentsList';
import NotificationCenter from './pages/Notifications/NotificationCenter';
import Reports from './pages/Reports/Reports';
import UsersList from './pages/Users/<USER>';
import UserForm from './pages/Users/<USER>';
import Settings from './pages/Settings/Settings';
import Profile from './pages/Profile/Profile';

// Styles
import './App.css';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <Router>
          <div className="App">
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<Login />} />
              
              {/* Protected Routes */}
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }>
                {/* Dashboard */}
                <Route index element={<Dashboard />} />
                
                {/* Subscriptions */}
                <Route path="subscriptions" element={<SubscriptionsList />} />
                <Route path="subscriptions/new" element={<SubscriptionForm />} />
                <Route path="subscriptions/:id" element={<SubscriptionDetails />} />
                <Route path="subscriptions/:id/edit" element={<SubscriptionForm />} />
                <Route path="subscriptions/analytics" element={<SubscriptionAnalytics />} />
                
                {/* Customers */}
                <Route path="customers" element={<CustomersList />} />
                <Route path="customers/new" element={<CustomerForm />} />
                <Route path="customers/:id" element={<CustomerDetails />} />
                <Route path="customers/:id/edit" element={<CustomerForm />} />
                
                {/* Invoices */}
                <Route path="invoices" element={<InvoicesList />} />
                <Route path="invoices/new" element={<InvoiceForm />} />
                <Route path="invoices/:id" element={<InvoiceDetails />} />
                <Route path="invoices/:id/edit" element={<InvoiceForm />} />
                
                {/* Payments */}
                <Route path="payments" element={<PaymentsList />} />
                
                {/* Notifications */}
                <Route path="notifications" element={<NotificationCenter />} />
                
                {/* Reports */}
                <Route path="reports" element={<Reports />} />
                
                {/* Users */}
                <Route path="users" element={<UsersList />} />
                <Route path="users/new" element={<UserForm />} />
                <Route path="users/:id/edit" element={<UserForm />} />
                
                {/* Settings */}
                <Route path="settings" element={<Settings />} />
                
                {/* Profile */}
                <Route path="profile" element={<Profile />} />
              </Route>
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            
            {/* Toast notifications */}
            <ToastContainer
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={true}
              pauseOnFocusLoss
              draggable
              pauseOnHover
              theme="light"
            />
          </div>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
