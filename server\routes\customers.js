const express = require('express');
const { body, validationResult } = require('express-validator');
const Customer = require('../models/Customer');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/customers
// @desc    Get all customers
// @access  Private (requires customers view permission)
router.get('/', auth, checkPermission('customers', 'view'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status, customerType } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (customerType) {
      query.customerType = customerType;
    }

    // Execute query with pagination
    const customers = await Customer.find(query)
      .populate('assignedTo', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Customer.countDocuments(query);

    res.json({
      customers,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/customers/:id
// @desc    Get customer by ID
// @access  Private (requires customers view permission)
router.get('/:id', auth, checkPermission('customers', 'view'), async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id)
      .populate('assignedTo', 'name email')
      .populate('subscriptionCount')
      .populate('totalRevenue');
    
    if (!customer) {
      return res.status(404).json({ message: 'العميل غير موجود' });
    }

    res.json({ customer });
  } catch (error) {
    console.error('Get customer error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/customers
// @desc    Create new customer
// @access  Private (requires customers create permission)
router.post('/', auth, checkPermission('customers', 'create'), [
  body('name').trim().isLength({ min: 2 }).withMessage('اسم العميل يجب أن يكون على الأقل حرفين'),
  body('email').isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('customerType').optional().isIn(['individual', 'business', 'enterprise']).withMessage('نوع العميل غير صحيح'),
  body('phone').optional().isMobilePhone().withMessage('رقم الهاتف غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const customerData = req.body;
    customerData.assignedTo = req.user.id;

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({ email: customerData.email });
    if (existingCustomer) {
      return res.status(400).json({ message: 'العميل موجود بالفعل' });
    }

    // Create new customer
    const customer = new Customer(customerData);
    await customer.save();

    res.status(201).json({
      message: 'تم إنشاء العميل بنجاح',
      customer
    });
  } catch (error) {
    console.error('Create customer error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/customers/:id
// @desc    Update customer
// @access  Private (requires customers edit permission)
router.put('/:id', auth, checkPermission('customers', 'edit'), [
  body('name').optional().trim().isLength({ min: 2 }).withMessage('اسم العميل يجب أن يكون على الأقل حرفين'),
  body('email').optional().isEmail().withMessage('البريد الإلكتروني غير صحيح'),
  body('customerType').optional().isIn(['individual', 'business', 'enterprise']).withMessage('نوع العميل غير صحيح'),
  body('phone').optional().isMobilePhone().withMessage('رقم الهاتف غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({ message: 'العميل غير موجود' });
    }

    // Check if email is already taken by another customer
    if (req.body.email && req.body.email !== customer.email) {
      const existingCustomer = await Customer.findOne({ email: req.body.email });
      if (existingCustomer) {
        return res.status(400).json({ message: 'البريد الإلكتروني مستخدم بالفعل' });
      }
    }

    // Update customer
    Object.assign(customer, req.body);
    await customer.save();

    res.json({
      message: 'تم تحديث العميل بنجاح',
      customer
    });
  } catch (error) {
    console.error('Update customer error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   DELETE /api/customers/:id
// @desc    Delete customer
// @access  Private (requires customers delete permission)
router.delete('/:id', auth, checkPermission('customers', 'delete'), async (req, res) => {
  try {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
      return res.status(404).json({ message: 'العميل غير موجود' });
    }

    // Check if customer has active subscriptions
    const Subscription = require('../models/Subscription');
    const activeSubscriptions = await Subscription.countDocuments({
      customer: req.params.id,
      status: 'active'
    });

    if (activeSubscriptions > 0) {
      return res.status(400).json({ 
        message: 'لا يمكن حذف العميل لوجود اشتراكات نشطة' 
      });
    }

    await Customer.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف العميل بنجاح' });
  } catch (error) {
    console.error('Delete customer error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/customers/:id/subscriptions
// @desc    Get customer subscriptions
// @access  Private (requires customers view permission)
router.get('/:id/subscriptions', auth, checkPermission('customers', 'view'), async (req, res) => {
  try {
    const Subscription = require('../models/Subscription');
    
    const subscriptions = await Subscription.find({ customer: req.params.id })
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 });

    res.json({ subscriptions });
  } catch (error) {
    console.error('Get customer subscriptions error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/customers/:id/invoices
// @desc    Get customer invoices
// @access  Private (requires customers view permission)
router.get('/:id/invoices', auth, checkPermission('customers', 'view'), async (req, res) => {
  try {
    const Invoice = require('../models/Invoice');
    
    const invoices = await Invoice.find({ customer: req.params.id })
      .populate('subscription', 'name')
      .sort({ createdAt: -1 });

    res.json({ invoices });
  } catch (error) {
    console.error('Get customer invoices error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
