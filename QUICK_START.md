# دليل البدء السريع - نظام إدارة الاشتراكات

## 🚀 التشغيل السريع

### 1. تثبيت المتطلبات
```bash
# تأكد من تثبيت Node.js (الإصدار 16+)
node --version

# تأكد من تثبيت MongoDB
mongod --version
```

### 2. تشغيل MongoDB
```bash
# تشغيل MongoDB
mongod

# أو إذا كان مثبت كخدمة
sudo systemctl start mongod  # Linux
brew services start mongodb-community  # macOS
```

### 3. تشغيل النظام
```bash
# الطريقة الأسرع - تشغيل مباشر
npm start

# أو الطريقة التقليدية
npm run setup  # إعداد أولي
npm run dev     # تشغيل النظام
```

## 📋 الوصول للنظام

بعد التشغيل الناجح:

- **واجهة المستخدم**: http://localhost:3000
- **API الخادم**: http://localhost:5000
- **قاعدة البيانات**: mongodb://localhost:27017/subscription_management

## 👤 إنشاء مستخدم مدير

### الطريقة الأولى: عبر API
```bash
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "المدير العام",
    "email": "<EMAIL>",
    "password": "123456",
    "role": "admin"
  }'
```

### الطريقة الثانية: عبر MongoDB مباشرة
```bash
mongo
use subscription_management

# إنشاء مستخدم مدير
db.users.insertOne({
  name: "المدير العام",
  email: "<EMAIL>",
  password: "$2a$12$hash_here", // استخدم bcrypt لتشفير كلمة المرور
  role: "admin",
  isActive: true,
  permissions: {
    dashboard: true,
    subscriptions: { view: true, create: true, edit: true, delete: true },
    customers: { view: true, create: true, edit: true, delete: true },
    invoices: { view: true, create: true, edit: true, delete: true },
    reports: { view: true, export: true },
    users: { view: true, create: true, edit: true, delete: true },
    settings: { view: true, edit: true }
  },
  createdAt: new Date(),
  updatedAt: new Date()
})
```

## 🔧 إعداد متغيرات البيئة

عدل ملف `server/.env`:

```env
# قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/subscription_management

# الخادم
PORT=5000
NODE_ENV=development

# JWT
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRE=30d

# البريد الإلكتروني (اختياري للبداية)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# الأمان
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

## 📊 بيانات تجريبية

لإضافة بيانات تجريبية للاختبار:

```javascript
// في MongoDB shell
use subscription_management

// إضافة عميل تجريبي
db.customers.insertOne({
  name: "شركة التقنية المتقدمة",
  email: "<EMAIL>",
  phone: "+966501234567",
  company: "شركة التقنية المتقدمة",
  customerType: "business",
  status: "active",
  registrationDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date()
})

// إضافة اشتراك تجريبي
db.subscriptions.insertOne({
  name: "اشتراك أساسي - خدمات السحابة",
  customer: ObjectId("customer_id_here"),
  serviceProvider: "AWS",
  subscriptionType: "basic",
  price: { amount: 500, currency: "SAR" },
  billingCycle: "monthly",
  startDate: new Date(),
  endDate: new Date(Date.now() + 365*24*60*60*1000),
  nextBillingDate: new Date(Date.now() + 30*24*60*60*1000),
  status: "active",
  accountingStatus: "not_billed",
  createdBy: ObjectId("admin_user_id_here"),
  createdAt: new Date(),
  updatedAt: new Date()
})
```

## 🎯 الخطوات التالية

1. **تسجيل الدخول** بحساب المدير
2. **إضافة عملاء** جدد من قسم إدارة العملاء
3. **إنشاء اشتراكات** للعملاء
4. **إصدار فواتير** وتتبع المدفوعات
5. **استكشاف التقارير** والتحليلات

## 🆘 حل المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
```bash
# تأكد من تشغيل MongoDB
sudo systemctl status mongod

# أو تشغيله يدوياً
mongod --dbpath /path/to/data/directory
```

### خطأ في المنافذ
```bash
# تحقق من المنافذ المستخدمة
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000

# إيقاف العمليات إذا لزم الأمر
sudo kill -9 $(lsof -t -i:3000)
sudo kill -9 $(lsof -t -i:5000)
```

### مشاكل التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules server/node_modules client/node_modules
npm run install-all
```

## 📞 الدعم

- راجع ملف `README.md` للتفاصيل الكاملة
- تحقق من ملفات الـ logs في حالة وجود أخطاء
- تأكد من تشغيل جميع الخدمات المطلوبة

---

**نصيحة**: احفظ هذا الملف كمرجع سريع للعودة إليه عند الحاجة! 🔖
