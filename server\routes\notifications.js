const express = require('express');
const { body, validationResult } = require('express-validator');
const Notification = require('../models/Notification');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/notifications
// @desc    Get all notifications
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { page = 1, limit = 10, type, category, status } = req.query;
    
    // Build query
    let query = {};
    
    if (type) {
      query.type = type;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (status) {
      query.status = status;
    }

    // Execute query with pagination
    const notifications = await Notification.find(query)
      .populate('createdBy', 'name email')
      .populate('relatedTo.entityId')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Notification.countDocuments(query);

    res.json({
      notifications,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/notifications/:id
// @desc    Get notification by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id)
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email')
      .populate('relatedTo.entityId');
    
    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    res.json({ notification });
  } catch (error) {
    console.error('Get notification error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/notifications
// @desc    Create new notification
// @access  Private
router.post('/', auth, [
  body('title').trim().isLength({ min: 1, max: 200 }).withMessage('العنوان مطلوب ويجب أن يكون أقل من 200 حرف'),
  body('message').trim().isLength({ min: 1, max: 1000 }).withMessage('النص مطلوب ويجب أن يكون أقل من 1000 حرف'),
  body('type').isIn(['email', 'sms', 'push', 'system']).withMessage('نوع الإشعار غير صحيح'),
  body('category').isIn(['subscription_renewal', 'payment_due', 'payment_received', 'subscription_expired', 'system_alert', 'custom']).withMessage('فئة الإشعار غير صحيحة'),
  body('recipients').isArray({ min: 1 }).withMessage('يجب إضافة مستلم واحد على الأقل'),
  body('recipients.*.recipientType').isIn(['customer', 'user', 'email']).withMessage('نوع المستلم غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const notificationData = {
      ...req.body,
      createdBy: req.user.id
    };

    // Create new notification
    const notification = new Notification(notificationData);
    await notification.save();

    // Populate the response
    await notification.populate('createdBy', 'name email');

    res.status(201).json({
      message: 'تم إنشاء الإشعار بنجاح',
      notification
    });
  } catch (error) {
    console.error('Create notification error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/notifications/:id
// @desc    Update notification
// @access  Private
router.put('/:id', auth, [
  body('title').optional().trim().isLength({ min: 1, max: 200 }).withMessage('العنوان يجب أن يكون أقل من 200 حرف'),
  body('message').optional().trim().isLength({ min: 1, max: 1000 }).withMessage('النص يجب أن يكون أقل من 1000 حرف'),
  body('type').optional().isIn(['email', 'sms', 'push', 'system']).withMessage('نوع الإشعار غير صحيح'),
  body('category').optional().isIn(['subscription_renewal', 'payment_due', 'payment_received', 'subscription_expired', 'system_alert', 'custom']).withMessage('فئة الإشعار غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    // Check if notification can be edited
    if (notification.status === 'sent') {
      return res.status(400).json({ message: 'لا يمكن تعديل إشعار تم إرساله' });
    }

    // Update notification
    Object.assign(notification, req.body);
    notification.lastModifiedBy = req.user.id;
    await notification.save();

    // Populate the response
    await notification.populate('createdBy', 'name email');
    await notification.populate('lastModifiedBy', 'name email');

    res.json({
      message: 'تم تحديث الإشعار بنجاح',
      notification
    });
  } catch (error) {
    console.error('Update notification error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   DELETE /api/notifications/:id
// @desc    Delete notification
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    // Check if notification can be deleted
    if (notification.status === 'sending') {
      return res.status(400).json({ message: 'لا يمكن حذف إشعار قيد الإرسال' });
    }

    await Notification.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف الإشعار بنجاح' });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/notifications/:id/send
// @desc    Send notification
// @access  Private
router.post('/:id/send', auth, async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    if (notification.status === 'sent') {
      return res.status(400).json({ message: 'تم إرسال الإشعار بالفعل' });
    }

    // Send notification
    await notification.send();

    res.json({
      message: 'تم إرسال الإشعار بنجاح',
      notification
    });
  } catch (error) {
    console.error('Send notification error:', error);
    res.status(500).json({ message: 'خطأ في إرسال الإشعار' });
  }
});

// @route   POST /api/notifications/:id/retry
// @desc    Retry failed notification
// @access  Private
router.post('/:id/retry', auth, async (req, res) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return res.status(404).json({ message: 'الإشعار غير موجود' });
    }

    if (notification.status !== 'failed') {
      return res.status(400).json({ message: 'يمكن إعادة المحاولة للإشعارات الفاشلة فقط' });
    }

    // Retry notification
    const retryResult = await notification.retry();

    if (retryResult) {
      res.json({
        message: 'تم إعادة إرسال الإشعار بنجاح',
        notification
      });
    } else {
      res.status(400).json({
        message: 'لا توجد محاولات متاحة للإعادة'
      });
    }
  } catch (error) {
    console.error('Retry notification error:', error);
    res.status(500).json({ message: 'خطأ في إعادة إرسال الإشعار' });
  }
});

// @route   GET /api/notifications/templates
// @desc    Get notification templates
// @access  Private
router.get('/templates', auth, async (req, res) => {
  try {
    // This would typically come from a database or configuration file
    const templates = [
      {
        id: 'subscription_renewal_reminder',
        name: 'تذكير تجديد الاشتراك',
        category: 'subscription_renewal',
        subject: 'تذكير: موعد تجديد اشتراكك قريب',
        body: 'عزيزي {{customerName}}، نود تذكيرك بأن اشتراكك {{subscriptionName}} سينتهي في {{daysLeft}} أيام.',
        variables: ['customerName', 'subscriptionName', 'daysLeft', 'renewalDate']
      },
      {
        id: 'payment_due_reminder',
        name: 'تذكير استحقاق دفعة',
        category: 'payment_due',
        subject: 'تذكير: لديك فاتورة مستحقة',
        body: 'عزيزي {{customerName}}، لديك فاتورة رقم {{invoiceNumber}} بمبلغ {{amount}} مستحقة في {{dueDate}}.',
        variables: ['customerName', 'invoiceNumber', 'amount', 'dueDate']
      },
      {
        id: 'payment_received_confirmation',
        name: 'تأكيد استلام الدفعة',
        category: 'payment_received',
        subject: 'تأكيد: تم استلام دفعتك',
        body: 'عزيزي {{customerName}}، تم استلام دفعتك بمبلغ {{amount}} للفاتورة رقم {{invoiceNumber}} بنجاح.',
        variables: ['customerName', 'amount', 'invoiceNumber', 'paymentDate']
      }
    ];

    res.json({ templates });
  } catch (error) {
    console.error('Get notification templates error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/notifications/analytics/overview
// @desc    Get notifications analytics overview
// @access  Private
router.get('/analytics/overview', auth, async (req, res) => {
  try {
    const totalNotifications = await Notification.countDocuments();
    const sentNotifications = await Notification.countDocuments({ status: 'sent' });
    const pendingNotifications = await Notification.countDocuments({ status: { $in: ['draft', 'scheduled'] } });
    const failedNotifications = await Notification.countDocuments({ status: 'failed' });

    // Delivery rate calculation
    const deliveryStats = await Notification.aggregate([
      {
        $match: { status: 'sent' }
      },
      {
        $project: {
          totalRecipients: { $size: '$recipients' },
          deliveredRecipients: {
            $size: {
              $filter: {
                input: '$recipients',
                cond: { $in: ['$$this.status', ['sent', 'delivered']] }
              }
            }
          }
        }
      },
      {
        $group: {
          _id: null,
          totalRecipients: { $sum: '$totalRecipients' },
          deliveredRecipients: { $sum: '$deliveredRecipients' }
        }
      }
    ]);

    const deliveryRate = deliveryStats[0] 
      ? Math.round((deliveryStats[0].deliveredRecipients / deliveryStats[0].totalRecipients) * 100)
      : 0;

    // Notifications by type
    const notificationsByType = await Notification.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      totalNotifications,
      sentNotifications,
      pendingNotifications,
      failedNotifications,
      deliveryRate,
      notificationsByType
    });
  } catch (error) {
    console.error('Get notifications analytics error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
