# Database Configuration
MONGODB_URI=mongodb://localhost:27017/subscription_management
DB_NAME=subscription_management

# Server Configuration
PORT=5000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# API Keys (for external integrations)
PAYMENT_GATEWAY_KEY=your_payment_gateway_key
SMS_API_KEY=your_sms_api_key

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
