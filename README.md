# نظام إدارة الاشتراكات المتكامل

نظام شامل لإدارة الاشتراكات والعملاء والفواتير مع واجهة مستخدم حديثة ومتجاوبة.

## المميزات الرئيسية

### 🏠 لوحة المعلومات
- إحصائيات شاملة للاشتراكات والعملاء والإيرادات
- رسوم بيانية تفاعلية لتتبع الأداء
- تنبيهات للاشتراكات المنتهية والفواتير المتأخرة

### 📋 إدارة الاشتراكات
- إنشاء وتعديل الاشتراكات
- تتبع حالة الاشتراكات (نشط، معلق، ملغي)
- تجديد الاشتراكات وإدارة دورات الفوترة
- تحليلات مفصلة للاشتراكات

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع معلومات الاتصال والشركات
- كشف حساب مفصل لكل عميل
- ربط العملاء بالاشتراكات والفواتير

### 🧾 إدارة الفواتير
- إنشاء فواتير تلقائية ويدوية
- تتبع حالة الدفع والمتأخرات
- إرسال الفواتير عبر البريد الإلكتروني
- تقارير مالية مفصلة

### 💳 إدارة المدفوعات
- تسجيل المدفوعات بطرق متعددة
- تتبع المعاملات والإيصالات
- إدارة المبالغ المستردة
- تحليلات الإيرادات

### 📧 مركز التواصل
- إرسال إشعارات للعملاء
- قوالب رسائل جاهزة
- تتبع معدل التسليم
- تذكيرات تلقائية

### 📊 التقارير والتحليلات
- تقارير مالية شاملة
- تحليلات نمو العملاء
- إحصائيات الاشتراكات
- تصدير البيانات

### 👤 إدارة المستخدمين
- نظام صلاحيات متقدم
- أدوار مختلفة (مدير، مشرف، موظف، مشاهد)
- تتبع نشاط المستخدمين

## التقنيات المستخدمة

### Frontend
- **React 18** - مكتبة واجهة المستخدم
- **React Router** - التنقل بين الصفحات
- **Styled Components** - تصميم المكونات
- **Recharts** - الرسوم البيانية
- **Axios** - طلبات HTTP
- **React Hook Form** - إدارة النماذج
- **React Toastify** - الإشعارات

### Backend
- **Node.js** - بيئة تشغيل JavaScript
- **Express.js** - إطار عمل الخادم
- **MongoDB** - قاعدة البيانات
- **Mongoose** - ODM لـ MongoDB
- **JWT** - المصادقة والتوكن
- **bcryptjs** - تشفير كلمات المرور
- **Express Validator** - التحقق من البيانات

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- MongoDB (الإصدار 5 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd newsystemmohammed
```

### 2. تثبيت التبعيات
```bash
# تثبيت جميع التبعيات
npm run install-all

# أو تثبيت كل جزء منفصل
npm install
cd server && npm install
cd ../client && npm install
```

### 3. إعداد قاعدة البيانات
```bash
# تأكد من تشغيل MongoDB
mongod

# إنشاء قاعدة البيانات
mongo
use subscription_management
```

### 4. إعداد متغيرات البيئة
```bash
# انسخ ملف البيئة النموذجي
cp server/.env.example server/.env

# عدل الملف وأضف القيم المطلوبة
```

### 5. تشغيل النظام
```bash
# تشغيل الخادم والعميل معاً
npm run dev

# أو تشغيل كل جزء منفصل
npm run server  # الخادم على المنفذ 5000
npm run client  # العميل على المنفذ 3000
```

## إعداد المتغيرات البيئية

أنشئ ملف `.env` في مجلد `server` مع المتغيرات التالية:

```env
# قاعدة البيانات
MONGODB_URI=mongodb://localhost:27017/subscription_management
DB_NAME=subscription_management

# الخادم
PORT=5000
NODE_ENV=development

# JWT
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d

# البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# الأمان
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

## إنشاء مستخدم مدير

بعد تشغيل النظام، يمكنك إنشاء مستخدم مدير من خلال:

```bash
# استخدم أداة مثل Postman أو curl
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "المدير",
    "email": "<EMAIL>",
    "password": "123456",
    "role": "admin"
  }'
```

## هيكل المشروع

```
newsystemmohammed/
├── client/                 # تطبيق React
│   ├── public/
│   ├── src/
│   │   ├── components/     # المكونات المشتركة
│   │   ├── contexts/       # Context API
│   │   ├── pages/          # صفحات التطبيق
│   │   └── ...
│   └── package.json
├── server/                 # خادم Express
│   ├── models/            # نماذج قاعدة البيانات
│   ├── routes/            # مسارات API
│   ├── middleware/        # Middleware
│   └── package.json
├── package.json           # إعدادات المشروع الرئيسي
└── README.md
```

## API Endpoints

### المصادقة
- `POST /api/auth/register` - تسجيل مستخدم جديد
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - الحصول على بيانات المستخدم الحالي

### الاشتراكات
- `GET /api/subscriptions` - قائمة الاشتراكات
- `POST /api/subscriptions` - إنشاء اشتراك جديد
- `GET /api/subscriptions/:id` - تفاصيل اشتراك
- `PUT /api/subscriptions/:id` - تحديث اشتراك
- `DELETE /api/subscriptions/:id` - حذف اشتراك

### العملاء
- `GET /api/customers` - قائمة العملاء
- `POST /api/customers` - إنشاء عميل جديد
- `GET /api/customers/:id` - تفاصيل عميل
- `PUT /api/customers/:id` - تحديث عميل

### الفواتير
- `GET /api/invoices` - قائمة الفواتير
- `POST /api/invoices` - إنشاء فاتورة جديدة
- `GET /api/invoices/:id` - تفاصيل فاتورة
- `POST /api/invoices/:id/mark-paid` - تحديد فاتورة كمدفوعة

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في المستودع.
