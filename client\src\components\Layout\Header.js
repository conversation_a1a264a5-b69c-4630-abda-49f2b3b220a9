import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { 
  FiMenu, 
  FiBell, 
  FiUser, 
  FiSettings, 
  FiLogOut,
  FiMoon,
  FiSun,
  FiGlobe,
  FiChevronDown
} from 'react-icons/fi';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const HeaderContainer = styled.header`
  height: 70px;
  background-color: var(--color-cardBg);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 100;
`;

const LeftSection = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
`;

const RightSection = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
`;

const MenuButton = styled.button`
  background: none;
  border: none;
  color: var(--color-textSecondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  
  &:hover {
    background-color: var(--color-backgroundTertiary);
    color: var(--color-textPrimary);
  }
  
  @media (min-width: 769px) {
    display: none;
  }
`;

const IconButton = styled.button`
  background: none;
  border: none;
  color: var(--color-textSecondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  position: relative;
  
  &:hover {
    background-color: var(--color-backgroundTertiary);
    color: var(--color-textPrimary);
  }
`;

const NotificationBadge = styled.span`
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: var(--color-error);
  color: white;
  border-radius: var(--radius-full);
  width: 18px;
  height: 18px;
  font-size: var(--text-xs);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
`;

const UserMenu = styled.div`
  position: relative;
`;

const UserButton = styled.button`
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-textPrimary);
  
  &:hover {
    background-color: var(--color-backgroundTertiary);
  }
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-sm);
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const UserName = styled.span`
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--color-textPrimary);
`;

const UserRole = styled.span`
  font-size: var(--text-xs);
  color: var(--color-textSecondary);
`;

const DropdownMenu = styled.div`
  position: absolute;
  top: 100%;
  right: ${props => props.isRTL ? '0' : 'auto'};
  left: ${props => props.isRTL ? 'auto' : '0'};
  margin-top: var(--spacing-sm);
  background-color: var(--color-cardBg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--color-cardShadow);
  min-width: 200px;
  z-index: 1000;
  opacity: ${props => props.show ? 1 : 0};
  visibility: ${props => props.show ? 'visible' : 'hidden'};
  transform: translateY(${props => props.show ? '0' : '-10px'});
  transition: all 0.2s ease;
`;

const DropdownItem = styled.button`
  width: 100%;
  background: none;
  border: none;
  padding: var(--spacing-md);
  text-align: right;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--color-textPrimary);
  
  &:hover {
    background-color: var(--color-backgroundTertiary);
  }
  
  &:first-child {
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }
  
  &:last-child {
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  }
`;

const DropdownDivider = styled.div`
  height: 1px;
  background-color: var(--color-border);
  margin: var(--spacing-sm) 0;
`;

const Header = ({ onToggleSidebar, onToggleMobileSidebar, sidebarCollapsed }) => {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationCount] = useState(3); // This would come from a notifications context
  const userMenuRef = useRef(null);
  const navigate = useNavigate();
  
  const { user, logout } = useAuth();
  const { currentTheme, toggleTheme, isRTL, toggleRTL } = useTheme();

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const getUserInitials = (name) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleDisplayName = (role) => {
    const roleNames = {
      admin: 'مدير النظام',
      manager: 'مشرف',
      employee: 'موظف',
      viewer: 'مشاهد'
    };
    return roleNames[role] || role;
  };

  return (
    <HeaderContainer>
      <LeftSection>
        <MenuButton onClick={onToggleMobileSidebar}>
          <FiMenu />
        </MenuButton>
      </LeftSection>

      <RightSection>
        {/* Theme Toggle */}
        <IconButton onClick={toggleTheme} title="تغيير المظهر">
          {currentTheme === 'light' ? <FiMoon /> : <FiSun />}
        </IconButton>

        {/* Language Toggle */}
        <IconButton onClick={toggleRTL} title="تغيير اللغة">
          <FiGlobe />
        </IconButton>

        {/* Notifications */}
        <IconButton title="الإشعارات">
          <FiBell />
          {notificationCount > 0 && (
            <NotificationBadge>
              {notificationCount > 9 ? '9+' : notificationCount}
            </NotificationBadge>
          )}
        </IconButton>

        {/* User Menu */}
        <UserMenu ref={userMenuRef}>
          <UserButton onClick={() => setUserMenuOpen(!userMenuOpen)}>
            <UserAvatar>
              {user?.avatar ? (
                <img src={user.avatar} alt={user.name} />
              ) : (
                getUserInitials(user?.name || 'User')
              )}
            </UserAvatar>
            
            <UserInfo>
              <UserName>{user?.name}</UserName>
              <UserRole>{getRoleDisplayName(user?.role)}</UserRole>
            </UserInfo>
            
            <FiChevronDown />
          </UserButton>

          <DropdownMenu show={userMenuOpen} isRTL={isRTL}>
            <DropdownItem as={Link} to="/profile" onClick={() => setUserMenuOpen(false)}>
              <FiUser />
              الملف الشخصي
            </DropdownItem>
            
            <DropdownItem as={Link} to="/settings" onClick={() => setUserMenuOpen(false)}>
              <FiSettings />
              الإعدادات
            </DropdownItem>
            
            <DropdownDivider />
            
            <DropdownItem onClick={handleLogout}>
              <FiLogOut />
              تسجيل الخروج
            </DropdownItem>
          </DropdownMenu>
        </UserMenu>
      </RightSection>
    </HeaderContainer>
  );
};

export default Header;
