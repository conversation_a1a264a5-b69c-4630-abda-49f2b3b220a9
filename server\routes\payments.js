const express = require('express');
const { body, validationResult } = require('express-validator');
const Payment = require('../models/Payment');
const Invoice = require('../models/Invoice');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/payments
// @desc    Get all payments
// @access  Private (requires invoices view permission)
router.get('/', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status, paymentMethod, customer } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { paymentId: { $regex: search, $options: 'i' } },
        { transactionId: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (paymentMethod) {
      query.paymentMethod = paymentMethod;
    }
    
    if (customer) {
      query.customer = customer;
    }

    // Execute query with pagination
    const payments = await Payment.find(query)
      .populate('customer', 'name email company')
      .populate('invoice', 'invoiceNumber totalAmount')
      .populate('subscription', 'name serviceProvider')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Payment.countDocuments(query);

    res.json({
      payments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get payments error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/payments/:id
// @desc    Get payment by ID
// @access  Private (requires invoices view permission)
router.get('/:id', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('customer', 'name email company phone')
      .populate('invoice', 'invoiceNumber totalAmount dueDate')
      .populate('subscription', 'name serviceProvider subscriptionType')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');
    
    if (!payment) {
      return res.status(404).json({ message: 'الدفعة غير موجودة' });
    }

    res.json({ payment });
  } catch (error) {
    console.error('Get payment error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/payments
// @desc    Create new payment
// @access  Private (requires invoices create permission)
router.post('/', auth, checkPermission('invoices', 'create'), [
  body('invoice').isMongoId().withMessage('معرف الفاتورة غير صحيح'),
  body('amount').isNumeric({ min: 0 }).withMessage('المبلغ يجب أن يكون رقماً موجباً'),
  body('paymentMethod').isIn(['credit_card', 'debit_card', 'bank_transfer', 'paypal', 'cash', 'check', 'wire_transfer']).withMessage('طريقة الدفع غير صحيحة'),
  body('paymentGateway').optional().isIn(['stripe', 'paypal', 'square', 'bank', 'manual']).withMessage('بوابة الدفع غير صحيحة'),
  body('paymentDate').optional().isISO8601().withMessage('تاريخ الدفع غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    // Check if invoice exists
    const invoice = await Invoice.findById(req.body.invoice).populate('customer subscription');
    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    const paymentData = {
      ...req.body,
      customer: invoice.customer._id,
      subscription: invoice.subscription._id,
      createdBy: req.user.id,
      paymentDate: req.body.paymentDate || new Date()
    };

    // Create new payment
    const payment = new Payment(paymentData);
    await payment.save();

    // Update invoice status if fully paid
    if (payment.amount >= invoice.totalAmount && payment.status === 'completed') {
      await invoice.markAsPaid({
        transactionId: payment.transactionId,
        paymentMethod: payment.paymentMethod
      });
    }

    // Populate the response
    await payment.populate('customer', 'name email company');
    await payment.populate('invoice', 'invoiceNumber totalAmount');
    await payment.populate('subscription', 'name serviceProvider');
    await payment.populate('createdBy', 'name email');

    res.status(201).json({
      message: 'تم إنشاء الدفعة بنجاح',
      payment
    });
  } catch (error) {
    console.error('Create payment error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/payments/:id
// @desc    Update payment
// @access  Private (requires invoices edit permission)
router.put('/:id', auth, checkPermission('invoices', 'edit'), [
  body('amount').optional().isNumeric({ min: 0 }).withMessage('المبلغ يجب أن يكون رقماً موجباً'),
  body('status').optional().isIn(['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded']).withMessage('حالة الدفع غير صحيحة'),
  body('paymentMethod').optional().isIn(['credit_card', 'debit_card', 'bank_transfer', 'paypal', 'cash', 'check', 'wire_transfer']).withMessage('طريقة الدفع غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return res.status(404).json({ message: 'الدفعة غير موجودة' });
    }

    // Update payment
    Object.assign(payment, req.body);
    payment.lastModifiedBy = req.user.id;
    
    if (req.body.status === 'completed' && payment.status !== 'completed') {
      payment.processedDate = new Date();
    }

    await payment.save();

    // Populate the response
    await payment.populate('customer', 'name email company');
    await payment.populate('invoice', 'invoiceNumber totalAmount');
    await payment.populate('subscription', 'name serviceProvider');
    await payment.populate('createdBy', 'name email');
    await payment.populate('lastModifiedBy', 'name email');

    res.json({
      message: 'تم تحديث الدفعة بنجاح',
      payment
    });
  } catch (error) {
    console.error('Update payment error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/payments/:id/refund
// @desc    Process payment refund
// @access  Private (requires invoices edit permission)
router.post('/:id/refund', auth, checkPermission('invoices', 'edit'), [
  body('amount').isNumeric({ min: 0 }).withMessage('مبلغ الاسترداد يجب أن يكون رقماً موجباً'),
  body('reason').trim().isLength({ min: 1 }).withMessage('سبب الاسترداد مطلوب')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return res.status(404).json({ message: 'الدفعة غير موجودة' });
    }

    if (payment.status !== 'completed') {
      return res.status(400).json({ message: 'لا يمكن استرداد دفعة غير مكتملة' });
    }

    const { amount, reason } = req.body;

    if (amount > payment.refundableAmount) {
      return res.status(400).json({ 
        message: `المبلغ المطلوب استرداده أكبر من المبلغ القابل للاسترداد (${payment.refundableAmount})` 
      });
    }

    await payment.processRefund({
      amount,
      reason,
      processedBy: req.user.id
    });

    res.json({
      message: 'تم معالجة الاسترداد بنجاح',
      payment
    });
  } catch (error) {
    console.error('Process refund error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/payments/analytics/overview
// @desc    Get payments analytics overview
// @access  Private (requires invoices view permission)
router.get('/analytics/overview', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const totalPayments = await Payment.countDocuments();
    const completedPayments = await Payment.countDocuments({ status: 'completed' });
    const pendingPayments = await Payment.countDocuments({ status: { $in: ['pending', 'processing'] } });
    const failedPayments = await Payment.countDocuments({ status: 'failed' });

    // Total amount processed
    const totalProcessed = await Payment.aggregate([
      { $match: { status: 'completed' } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    // Monthly processed amount
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);
    
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const monthlyProcessed = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          processedDate: { $gte: currentMonth, $lt: nextMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    // Payment methods breakdown
    const paymentMethodsBreakdown = await Payment.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: '$paymentMethod',
          count: { $sum: 1 },
          total: { $sum: '$amount' }
        }
      }
    ]);

    res.json({
      totalPayments,
      completedPayments,
      pendingPayments,
      failedPayments,
      totalProcessed: totalProcessed[0]?.total || 0,
      monthlyProcessed: monthlyProcessed[0]?.total || 0,
      paymentMethodsBreakdown
    });
  } catch (error) {
    console.error('Get payments analytics error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
