const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'الاسم مطلوب'],
    trim: true,
    maxlength: [100, 'الاسم يجب أن يكون أقل من 100 حرف']
  },
  email: {
    type: String,
    required: [true, 'البريد الإلكتروني مطلوب'],
    unique: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'البريد الإلكتروني غير صحيح']
  },
  password: {
    type: String,
    required: [true, 'كلمة المرور مطلوبة'],
    minlength: [6, 'كلمة المرور يجب أن تكون على الأقل 6 أحرف']
  },
  role: {
    type: String,
    enum: ['admin', 'manager', 'employee', 'viewer'],
    default: 'employee',
    required: true
  },
  permissions: {
    dashboard: { type: Boolean, default: true },
    subscriptions: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    customers: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    invoices: {
      view: { type: Boolean, default: true },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    reports: {
      view: { type: Boolean, default: true },
      export: { type: Boolean, default: false }
    },
    users: {
      view: { type: Boolean, default: false },
      create: { type: Boolean, default: false },
      edit: { type: Boolean, default: false },
      delete: { type: Boolean, default: false }
    },
    settings: {
      view: { type: Boolean, default: false },
      edit: { type: Boolean, default: false }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  avatar: {
    type: String,
    default: null
  },
  phone: {
    type: String,
    trim: true
  },
  department: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Set default permissions based on role
userSchema.pre('save', function(next) {
  if (this.isModified('role')) {
    switch (this.role) {
      case 'admin':
        this.permissions = {
          dashboard: true,
          subscriptions: { view: true, create: true, edit: true, delete: true },
          customers: { view: true, create: true, edit: true, delete: true },
          invoices: { view: true, create: true, edit: true, delete: true },
          reports: { view: true, export: true },
          users: { view: true, create: true, edit: true, delete: true },
          settings: { view: true, edit: true }
        };
        break;
      case 'manager':
        this.permissions = {
          dashboard: true,
          subscriptions: { view: true, create: true, edit: true, delete: false },
          customers: { view: true, create: true, edit: true, delete: false },
          invoices: { view: true, create: true, edit: true, delete: false },
          reports: { view: true, export: true },
          users: { view: true, create: false, edit: false, delete: false },
          settings: { view: true, edit: false }
        };
        break;
      case 'employee':
        this.permissions = {
          dashboard: true,
          subscriptions: { view: true, create: true, edit: true, delete: false },
          customers: { view: true, create: true, edit: true, delete: false },
          invoices: { view: true, create: true, edit: false, delete: false },
          reports: { view: true, export: false },
          users: { view: false, create: false, edit: false, delete: false },
          settings: { view: false, edit: false }
        };
        break;
      case 'viewer':
        this.permissions = {
          dashboard: true,
          subscriptions: { view: true, create: false, edit: false, delete: false },
          customers: { view: true, create: false, edit: false, delete: false },
          invoices: { view: true, create: false, edit: false, delete: false },
          reports: { view: true, export: false },
          users: { view: false, create: false, edit: false, delete: false },
          settings: { view: false, edit: false }
        };
        break;
    }
  }
  next();
});

module.exports = mongoose.model('User', userSchema);
