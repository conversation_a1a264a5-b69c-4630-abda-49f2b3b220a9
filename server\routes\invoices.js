const express = require('express');
const { body, validationResult } = require('express-validator');
const Invoice = require('../models/Invoice');
const Subscription = require('../models/Subscription');
const Customer = require('../models/Customer');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/invoices
// @desc    Get all invoices
// @access  Private (requires invoices view permission)
router.get('/', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status, customer } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { invoiceNumber: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (customer) {
      query.customer = customer;
    }

    // Execute query with pagination
    const invoices = await Invoice.find(query)
      .populate('customer', 'name email company')
      .populate('subscription', 'name serviceProvider')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Invoice.countDocuments(query);

    res.json({
      invoices,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get invoices error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/invoices/:id
// @desc    Get invoice by ID
// @access  Private (requires invoices view permission)
router.get('/:id', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('customer', 'name email company phone address')
      .populate('subscription', 'name serviceProvider subscriptionType')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');
    
    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    res.json({ invoice });
  } catch (error) {
    console.error('Get invoice error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/invoices
// @desc    Create new invoice
// @access  Private (requires invoices create permission)
router.post('/', auth, checkPermission('invoices', 'create'), [
  body('customer').isMongoId().withMessage('معرف العميل غير صحيح'),
  body('subscription').isMongoId().withMessage('معرف الاشتراك غير صحيح'),
  body('dueDate').isISO8601().withMessage('تاريخ الاستحقاق غير صحيح'),
  body('items').isArray({ min: 1 }).withMessage('يجب إضافة عنصر واحد على الأقل'),
  body('items.*.description').trim().isLength({ min: 1 }).withMessage('وصف العنصر مطلوب'),
  body('items.*.quantity').isNumeric({ min: 0 }).withMessage('الكمية يجب أن تكون رقماً موجباً'),
  body('items.*.unitPrice').isNumeric({ min: 0 }).withMessage('سعر الوحدة يجب أن يكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    // Check if customer exists
    const customer = await Customer.findById(req.body.customer);
    if (!customer) {
      return res.status(404).json({ message: 'العميل غير موجود' });
    }

    // Check if subscription exists
    const subscription = await Subscription.findById(req.body.subscription);
    if (!subscription) {
      return res.status(404).json({ message: 'الاشتراك غير موجود' });
    }

    const invoiceData = {
      ...req.body,
      createdBy: req.user.id,
      billingAddress: {
        name: customer.name,
        company: customer.company,
        ...customer.address
      }
    };

    // Create new invoice
    const invoice = new Invoice(invoiceData);
    await invoice.save();

    // Populate the response
    await invoice.populate('customer', 'name email company');
    await invoice.populate('subscription', 'name serviceProvider');
    await invoice.populate('createdBy', 'name email');

    res.status(201).json({
      message: 'تم إنشاء الفاتورة بنجاح',
      invoice
    });
  } catch (error) {
    console.error('Create invoice error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/invoices/:id
// @desc    Update invoice
// @access  Private (requires invoices edit permission)
router.put('/:id', auth, checkPermission('invoices', 'edit'), [
  body('dueDate').optional().isISO8601().withMessage('تاريخ الاستحقاق غير صحيح'),
  body('items').optional().isArray({ min: 1 }).withMessage('يجب إضافة عنصر واحد على الأقل'),
  body('items.*.description').optional().trim().isLength({ min: 1 }).withMessage('وصف العنصر مطلوب'),
  body('items.*.quantity').optional().isNumeric({ min: 0 }).withMessage('الكمية يجب أن تكون رقماً موجباً'),
  body('items.*.unitPrice').optional().isNumeric({ min: 0 }).withMessage('سعر الوحدة يجب أن يكون رقماً موجباً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    // Check if invoice can be edited
    if (invoice.status === 'paid') {
      return res.status(400).json({ message: 'لا يمكن تعديل فاتورة مدفوعة' });
    }

    // Update invoice
    Object.assign(invoice, req.body);
    invoice.lastModifiedBy = req.user.id;
    await invoice.save();

    // Populate the response
    await invoice.populate('customer', 'name email company');
    await invoice.populate('subscription', 'name serviceProvider');
    await invoice.populate('createdBy', 'name email');
    await invoice.populate('lastModifiedBy', 'name email');

    res.json({
      message: 'تم تحديث الفاتورة بنجاح',
      invoice
    });
  } catch (error) {
    console.error('Update invoice error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   DELETE /api/invoices/:id
// @desc    Delete invoice
// @access  Private (requires invoices delete permission)
router.delete('/:id', auth, checkPermission('invoices', 'delete'), async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    // Check if invoice can be deleted
    if (invoice.status === 'paid') {
      return res.status(400).json({ message: 'لا يمكن حذف فاتورة مدفوعة' });
    }

    await Invoice.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف الفاتورة بنجاح' });
  } catch (error) {
    console.error('Delete invoice error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/invoices/:id/mark-paid
// @desc    Mark invoice as paid
// @access  Private (requires invoices edit permission)
router.post('/:id/mark-paid', auth, checkPermission('invoices', 'edit'), [
  body('paymentMethod').optional().isIn(['credit_card', 'bank_transfer', 'paypal', 'cash', 'check']).withMessage('طريقة الدفع غير صحيحة'),
  body('transactionId').optional().trim().isLength({ min: 1 }).withMessage('معرف المعاملة مطلوب'),
  body('paidDate').optional().isISO8601().withMessage('تاريخ الدفع غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const invoice = await Invoice.findById(req.params.id);

    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    if (invoice.status === 'paid') {
      return res.status(400).json({ message: 'الفاتورة مدفوعة بالفعل' });
    }

    const paymentDetails = {
      paymentMethod: req.body.paymentMethod,
      transactionId: req.body.transactionId,
      paidDate: req.body.paidDate || new Date()
    };

    await invoice.markAsPaid(paymentDetails);

    res.json({
      message: 'تم تحديث حالة الفاتورة إلى مدفوعة',
      invoice
    });
  } catch (error) {
    console.error('Mark invoice as paid error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/invoices/:id/send
// @desc    Send invoice to customer
// @access  Private (requires invoices edit permission)
router.post('/:id/send', auth, checkPermission('invoices', 'edit'), async (req, res) => {
  try {
    const invoice = await Invoice.findById(req.params.id)
      .populate('customer', 'name email');

    if (!invoice) {
      return res.status(404).json({ message: 'الفاتورة غير موجودة' });
    }

    if (invoice.status === 'paid') {
      return res.status(400).json({ message: 'لا يمكن إرسال فاتورة مدفوعة' });
    }

    // Update invoice status and email sent info
    invoice.status = 'sent';
    invoice.emailSent = {
      sent: true,
      sentDate: new Date(),
      sentTo: invoice.customer.email,
      attempts: (invoice.emailSent.attempts || 0) + 1
    };

    await invoice.save();

    // Here you would implement the actual email sending logic
    // For now, we'll just simulate it

    res.json({
      message: 'تم إرسال الفاتورة بنجاح',
      invoice
    });
  } catch (error) {
    console.error('Send invoice error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/invoices/analytics/overview
// @desc    Get invoices analytics overview
// @access  Private (requires invoices view permission)
router.get('/analytics/overview', auth, checkPermission('invoices', 'view'), async (req, res) => {
  try {
    const totalInvoices = await Invoice.countDocuments();
    const paidInvoices = await Invoice.countDocuments({ status: 'paid' });
    const pendingInvoices = await Invoice.countDocuments({ status: { $in: ['draft', 'sent'] } });
    const overdueInvoices = await Invoice.countDocuments({ 
      status: { $ne: 'paid' },
      dueDate: { $lt: new Date() }
    });

    // Total revenue calculation
    const totalRevenue = await Invoice.aggregate([
      { $match: { status: 'paid' } },
      { $group: { _id: null, total: { $sum: '$totalAmount' } } }
    ]);

    // Monthly revenue calculation
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);
    
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const monthlyRevenue = await Invoice.aggregate([
      {
        $match: {
          status: 'paid',
          paidDate: { $gte: currentMonth, $lt: nextMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$totalAmount' }
        }
      }
    ]);

    res.json({
      totalInvoices,
      paidInvoices,
      pendingInvoices,
      overdueInvoices,
      totalRevenue: totalRevenue[0]?.total || 0,
      monthlyRevenue: monthlyRevenue[0]?.total || 0
    });
  } catch (error) {
    console.error('Get invoices analytics error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
