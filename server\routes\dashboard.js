const express = require('express');
const Subscription = require('../models/Subscription');
const Customer = require('../models/Customer');
const Invoice = require('../models/Invoice');
const Payment = require('../models/Payment');
const { auth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    // Get current date ranges
    const now = new Date();
    const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // Subscriptions stats
    const totalSubscriptions = await Subscription.countDocuments();
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    const newSubscriptionsThisMonth = await Subscription.countDocuments({
      createdAt: { $gte: currentMonth }
    });
    const newSubscriptionsLastMonth = await Subscription.countDocuments({
      createdAt: { $gte: lastMonth, $lt: currentMonth }
    });

    // Customers stats
    const totalCustomers = await Customer.countDocuments();
    const newCustomersThisMonth = await Customer.countDocuments({
      registrationDate: { $gte: currentMonth }
    });
    const newCustomersLastMonth = await Customer.countDocuments({
      registrationDate: { $gte: lastMonth, $lt: currentMonth }
    });

    // Revenue stats
    const monthlyRevenueResult = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: currentMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    const lastMonthRevenueResult = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: lastMonth, $lt: currentMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$amount' }
        }
      }
    ]);

    const monthlyRevenue = monthlyRevenueResult[0]?.total || 0;
    const lastMonthRevenue = lastMonthRevenueResult[0]?.total || 0;

    // Invoices stats
    const pendingInvoices = await Invoice.countDocuments({
      status: { $in: ['draft', 'sent'] }
    });
    const overdueInvoices = await Invoice.countDocuments({
      status: { $ne: 'paid' },
      dueDate: { $lt: now }
    });

    // Calculate growth percentages
    const subscriptionGrowth = newSubscriptionsLastMonth > 0 
      ? Math.round(((newSubscriptionsThisMonth - newSubscriptionsLastMonth) / newSubscriptionsLastMonth) * 100)
      : newSubscriptionsThisMonth > 0 ? 100 : 0;

    const customerGrowth = newCustomersLastMonth > 0
      ? Math.round(((newCustomersThisMonth - newCustomersLastMonth) / newCustomersLastMonth) * 100)
      : newCustomersThisMonth > 0 ? 100 : 0;

    const revenueGrowth = lastMonthRevenue > 0
      ? Math.round(((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100)
      : monthlyRevenue > 0 ? 100 : 0;

    res.json({
      subscriptions: {
        total: totalSubscriptions,
        active: activeSubscriptions,
        thisMonth: newSubscriptionsThisMonth,
        growth: subscriptionGrowth
      },
      customers: {
        total: totalCustomers,
        thisMonth: newCustomersThisMonth,
        growth: customerGrowth
      },
      revenue: {
        thisMonth: monthlyRevenue,
        growth: revenueGrowth
      },
      invoices: {
        pending: pendingInvoices,
        overdue: overdueInvoices
      }
    });
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/charts/revenue
// @desc    Get revenue chart data
// @access  Private
router.get('/charts/revenue', auth, async (req, res) => {
  try {
    const { months = 6 } = req.query;
    
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    const revenueData = await Payment.aggregate([
      {
        $match: {
          status: 'completed',
          paymentDate: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$paymentDate' },
            month: { $month: '$paymentDate' }
          },
          revenue: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Format data for chart
    const chartData = revenueData.map(item => ({
      month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
      revenue: item.revenue,
      transactions: item.count
    }));

    res.json({ chartData });
  } catch (error) {
    console.error('Get revenue chart error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/charts/subscriptions
// @desc    Get subscriptions chart data
// @access  Private
router.get('/charts/subscriptions', auth, async (req, res) => {
  try {
    const { months = 6 } = req.query;
    
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - parseInt(months));

    const subscriptionData = await Subscription.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          newSubscriptions: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Format data for chart
    const chartData = subscriptionData.map(item => ({
      month: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
      subscriptions: item.newSubscriptions
    }));

    res.json({ chartData });
  } catch (error) {
    console.error('Get subscriptions chart error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/charts/subscription-types
// @desc    Get subscription types distribution
// @access  Private
router.get('/charts/subscription-types', auth, async (req, res) => {
  try {
    const subscriptionTypes = await Subscription.aggregate([
      {
        $group: {
          _id: '$subscriptionType',
          count: { $sum: 1 },
          revenue: { $sum: '$price.amount' }
        }
      }
    ]);

    // Format data for pie chart
    const chartData = subscriptionTypes.map(item => ({
      name: item._id,
      value: item.count,
      revenue: item.revenue
    }));

    res.json({ chartData });
  } catch (error) {
    console.error('Get subscription types chart error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/recent-activities
// @desc    Get recent activities
// @access  Private
router.get('/recent-activities', auth, async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // Get recent subscriptions
    const recentSubscriptions = await Subscription.find()
      .populate('customer', 'name')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit) / 2)
      .select('name customer createdAt status');

    // Get recent payments
    const recentPayments = await Payment.find({ status: 'completed' })
      .populate('customer', 'name')
      .sort({ paymentDate: -1 })
      .limit(parseInt(limit) / 2)
      .select('amount customer paymentDate');

    // Combine and format activities
    const activities = [];

    recentSubscriptions.forEach(sub => {
      activities.push({
        id: sub._id,
        type: 'subscription',
        title: 'اشتراك جديد',
        description: `اشتراك جديد: ${sub.name}`,
        customer: sub.customer?.name,
        date: sub.createdAt,
        status: sub.status
      });
    });

    recentPayments.forEach(payment => {
      activities.push({
        id: payment._id,
        type: 'payment',
        title: 'دفعة جديدة',
        description: `تم استلام دفعة بقيمة ${payment.amount}`,
        customer: payment.customer?.name,
        date: payment.paymentDate,
        amount: payment.amount
      });
    });

    // Sort by date and limit
    activities.sort((a, b) => new Date(b.date) - new Date(a.date));
    const limitedActivities = activities.slice(0, parseInt(limit));

    res.json({ activities: limitedActivities });
  } catch (error) {
    console.error('Get recent activities error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/upcoming-renewals
// @desc    Get upcoming subscription renewals
// @access  Private
router.get('/upcoming-renewals', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + parseInt(days));

    const upcomingRenewals = await Subscription.find({
      status: 'active',
      nextBillingDate: { $lte: futureDate }
    })
    .populate('customer', 'name email')
    .sort({ nextBillingDate: 1 })
    .select('name customer nextBillingDate price serviceProvider');

    res.json({ upcomingRenewals });
  } catch (error) {
    console.error('Get upcoming renewals error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/dashboard/overdue-invoices
// @desc    Get overdue invoices
// @access  Private
router.get('/overdue-invoices', auth, async (req, res) => {
  try {
    const now = new Date();

    const overdueInvoices = await Invoice.find({
      status: { $ne: 'paid' },
      dueDate: { $lt: now }
    })
    .populate('customer', 'name email')
    .populate('subscription', 'name')
    .sort({ dueDate: 1 })
    .select('invoiceNumber customer subscription totalAmount dueDate status');

    res.json({ overdueInvoices });
  } catch (error) {
    console.error('Get overdue invoices error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
