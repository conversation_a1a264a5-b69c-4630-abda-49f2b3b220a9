const mongoose = require('mongoose');

const subscriptionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'اسم الاشتراك مطلوب'],
    trim: true,
    maxlength: [100, 'اسم الاشتراك يجب أن يكون أقل من 100 حرف']
  },
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'العميل مطلوب']
  },
  serviceProvider: {
    type: String,
    required: [true, 'مزود الخدمة مطلوب'],
    trim: true
  },
  subscriptionType: {
    type: String,
    required: [true, 'نوع الاشتراك مطلوب'],
    enum: ['basic', 'premium', 'enterprise', 'custom'],
    default: 'basic'
  },
  price: {
    amount: {
      type: Number,
      required: [true, 'سعر الاشتراك مطلوب'],
      min: [0, 'السعر يجب أن يكون أكبر من أو يساوي صفر'],
      get: v => Math.round(v * 100) / 100,
      set: v => Math.round(v * 100) / 100
    },
    currency: {
      type: String,
      enum: ['USD', 'EUR', 'SAR', 'AED', 'EGP'],
      default: 'USD'
    }
  },
  billingCycle: {
    type: String,
    enum: ['monthly', 'quarterly', 'semi-annual', 'annual'],
    default: 'monthly',
    required: true
  },
  startDate: {
    type: Date,
    required: [true, 'تاريخ بداية الاشتراك مطلوب'],
    default: Date.now
  },
  endDate: {
    type: Date,
    required: [true, 'تاريخ انتهاء الاشتراك مطلوب']
  },
  nextBillingDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'cancelled', 'expired'],
    default: 'active'
  },
  accountingStatus: {
    type: String,
    enum: ['not_billed', 'billed', 'paid', 'overdue'],
    default: 'not_billed'
  },
  technicalDetails: {
    apiKey: {
      type: String,
      trim: true,
      select: false // Don't include in regular queries for security
    },
    port: {
      type: Number,
      min: 1,
      max: 65535
    },
    serverName: {
      type: String,
      trim: true
    },
    cloudIP: {
      type: String,
      trim: true,
      match: [/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/, 'عنوان IP غير صحيح']
    },
    serverLocation: {
      type: String,
      trim: true
    },
    bandwidth: {
      type: String,
      trim: true
    },
    storage: {
      type: String,
      trim: true
    }
  },
  features: [{
    name: { type: String, required: true },
    enabled: { type: Boolean, default: true },
    limit: { type: String },
    description: { type: String }
  }],
  autoRenewal: {
    enabled: {
      type: Boolean,
      default: true
    },
    notifyBefore: {
      type: Number,
      default: 7, // days
      min: 1,
      max: 30
    }
  },
  discounts: [{
    type: {
      type: String,
      enum: ['percentage', 'fixed_amount'],
      required: true
    },
    value: {
      type: Number,
      required: true,
      min: 0
    },
    description: {
      type: String,
      required: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    },
    isActive: {
      type: Boolean,
      default: true
    }
  }],
  notes: {
    type: String,
    maxlength: [1000, 'الملاحظات يجب أن تكون أقل من 1000 حرف']
  },
  tags: [{
    type: String,
    trim: true
  }],
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  lastModifiedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  renewalHistory: [{
    date: { type: Date, required: true },
    amount: { type: Number, required: true },
    status: { 
      type: String, 
      enum: ['له', 'عليه'], 
      required: true 
    },
    description: { type: String },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }]
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for better performance
subscriptionSchema.index({ customer: 1 });
subscriptionSchema.index({ status: 1 });
subscriptionSchema.index({ nextBillingDate: 1 });
subscriptionSchema.index({ endDate: 1 });
subscriptionSchema.index({ serviceProvider: 1 });
subscriptionSchema.index({ subscriptionType: 1 });
subscriptionSchema.index({ createdAt: -1 });

// Calculate next billing date based on billing cycle
subscriptionSchema.methods.calculateNextBillingDate = function() {
  const currentDate = this.nextBillingDate || this.startDate;
  const nextDate = new Date(currentDate);
  
  switch (this.billingCycle) {
    case 'monthly':
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    case 'quarterly':
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    case 'semi-annual':
      nextDate.setMonth(nextDate.getMonth() + 6);
      break;
    case 'annual':
      nextDate.setFullYear(nextDate.getFullYear() + 1);
      break;
  }
  
  return nextDate;
};

// Calculate effective price after discounts
subscriptionSchema.methods.getEffectivePrice = function() {
  let effectivePrice = this.price.amount;
  const currentDate = new Date();
  
  this.discounts.forEach(discount => {
    if (discount.isActive && 
        currentDate >= discount.startDate && 
        currentDate <= discount.endDate) {
      if (discount.type === 'percentage') {
        effectivePrice -= (effectivePrice * discount.value / 100);
      } else if (discount.type === 'fixed_amount') {
        effectivePrice -= discount.value;
      }
    }
  });
  
  return Math.max(0, Math.round(effectivePrice * 100) / 100);
};

// Pre-save middleware to set end date and next billing date
subscriptionSchema.pre('save', function(next) {
  if (this.isNew || this.isModified('startDate') || this.isModified('billingCycle')) {
    if (!this.nextBillingDate) {
      this.nextBillingDate = this.calculateNextBillingDate();
    }
    
    if (!this.endDate) {
      // Set end date to one year from start date by default
      this.endDate = new Date(this.startDate);
      this.endDate.setFullYear(this.endDate.getFullYear() + 1);
    }
  }
  next();
});

module.exports = mongoose.model('Subscription', subscriptionSchema);
