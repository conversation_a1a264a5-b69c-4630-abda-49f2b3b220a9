const express = require('express');
const { body, validationResult } = require('express-validator');
const Subscription = require('../models/Subscription');
const Customer = require('../models/Customer');
const { auth, checkPermission } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/subscriptions
// @desc    Get all subscriptions
// @access  Private (requires subscriptions view permission)
router.get('/', auth, checkPermission('subscriptions', 'view'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status, subscriptionType, serviceProvider } = req.query;
    
    // Build query
    let query = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { serviceProvider: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      query.status = status;
    }
    
    if (subscriptionType) {
      query.subscriptionType = subscriptionType;
    }
    
    if (serviceProvider) {
      query.serviceProvider = serviceProvider;
    }

    // Execute query with pagination
    const subscriptions = await Subscription.find(query)
      .populate('customer', 'name email company')
      .populate('createdBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Subscription.countDocuments(query);

    res.json({
      subscriptions,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    console.error('Get subscriptions error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/subscriptions/:id
// @desc    Get subscription by ID
// @access  Private (requires subscriptions view permission)
router.get('/:id', auth, checkPermission('subscriptions', 'view'), async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id)
      .populate('customer', 'name email company phone')
      .populate('createdBy', 'name email')
      .populate('lastModifiedBy', 'name email');
    
    if (!subscription) {
      return res.status(404).json({ message: 'الاشتراك غير موجود' });
    }

    res.json({ subscription });
  } catch (error) {
    console.error('Get subscription error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/subscriptions
// @desc    Create new subscription
// @access  Private (requires subscriptions create permission)
router.post('/', auth, checkPermission('subscriptions', 'create'), [
  body('name').trim().isLength({ min: 2 }).withMessage('اسم الاشتراك يجب أن يكون على الأقل حرفين'),
  body('customer').isMongoId().withMessage('معرف العميل غير صحيح'),
  body('serviceProvider').trim().isLength({ min: 2 }).withMessage('مزود الخدمة مطلوب'),
  body('subscriptionType').isIn(['basic', 'premium', 'enterprise', 'custom']).withMessage('نوع الاشتراك غير صحيح'),
  body('price.amount').isNumeric().withMessage('سعر الاشتراك يجب أن يكون رقماً'),
  body('billingCycle').isIn(['monthly', 'quarterly', 'semi-annual', 'annual']).withMessage('دورة الفوترة غير صحيحة'),
  body('startDate').isISO8601().withMessage('تاريخ البداية غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    // Check if customer exists
    const customer = await Customer.findById(req.body.customer);
    if (!customer) {
      return res.status(404).json({ message: 'العميل غير موجود' });
    }

    const subscriptionData = {
      ...req.body,
      createdBy: req.user.id
    };

    // Create new subscription
    const subscription = new Subscription(subscriptionData);
    await subscription.save();

    // Populate the response
    await subscription.populate('customer', 'name email company');
    await subscription.populate('createdBy', 'name email');

    res.status(201).json({
      message: 'تم إنشاء الاشتراك بنجاح',
      subscription
    });
  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   PUT /api/subscriptions/:id
// @desc    Update subscription
// @access  Private (requires subscriptions edit permission)
router.put('/:id', auth, checkPermission('subscriptions', 'edit'), [
  body('name').optional().trim().isLength({ min: 2 }).withMessage('اسم الاشتراك يجب أن يكون على الأقل حرفين'),
  body('customer').optional().isMongoId().withMessage('معرف العميل غير صحيح'),
  body('serviceProvider').optional().trim().isLength({ min: 2 }).withMessage('مزود الخدمة مطلوب'),
  body('subscriptionType').optional().isIn(['basic', 'premium', 'enterprise', 'custom']).withMessage('نوع الاشتراك غير صحيح'),
  body('price.amount').optional().isNumeric().withMessage('سعر الاشتراك يجب أن يكون رقماً'),
  body('billingCycle').optional().isIn(['monthly', 'quarterly', 'semi-annual', 'annual']).withMessage('دورة الفوترة غير صحيحة')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({ message: 'الاشتراك غير موجود' });
    }

    // Check if customer exists (if customer is being updated)
    if (req.body.customer) {
      const customer = await Customer.findById(req.body.customer);
      if (!customer) {
        return res.status(404).json({ message: 'العميل غير موجود' });
      }
    }

    // Update subscription
    Object.assign(subscription, req.body);
    subscription.lastModifiedBy = req.user.id;
    await subscription.save();

    // Populate the response
    await subscription.populate('customer', 'name email company');
    await subscription.populate('createdBy', 'name email');
    await subscription.populate('lastModifiedBy', 'name email');

    res.json({
      message: 'تم تحديث الاشتراك بنجاح',
      subscription
    });
  } catch (error) {
    console.error('Update subscription error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   DELETE /api/subscriptions/:id
// @desc    Delete subscription
// @access  Private (requires subscriptions delete permission)
router.delete('/:id', auth, checkPermission('subscriptions', 'delete'), async (req, res) => {
  try {
    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({ message: 'الاشتراك غير موجود' });
    }

    // Check if subscription has active invoices
    const Invoice = require('../models/Invoice');
    const activeInvoices = await Invoice.countDocuments({
      subscription: req.params.id,
      status: { $in: ['sent', 'overdue'] }
    });

    if (activeInvoices > 0) {
      return res.status(400).json({ 
        message: 'لا يمكن حذف الاشتراك لوجود فواتير نشطة' 
      });
    }

    await Subscription.findByIdAndDelete(req.params.id);

    res.json({ message: 'تم حذف الاشتراك بنجاح' });
  } catch (error) {
    console.error('Delete subscription error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   POST /api/subscriptions/:id/renew
// @desc    Renew subscription
// @access  Private (requires subscriptions edit permission)
router.post('/:id/renew', auth, checkPermission('subscriptions', 'edit'), [
  body('amount').isNumeric().withMessage('المبلغ يجب أن يكون رقماً'),
  body('status').isIn(['له', 'عليه']).withMessage('حالة التجديد غير صحيحة'),
  body('description').optional().trim().isLength({ max: 500 }).withMessage('الوصف طويل جداً')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ 
        message: 'بيانات غير صحيحة', 
        errors: errors.array() 
      });
    }

    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({ message: 'الاشتراك غير موجود' });
    }

    const { amount, status, description } = req.body;

    // Add renewal record
    subscription.renewalHistory.push({
      date: new Date(),
      amount,
      status,
      description,
      createdBy: req.user.id
    });

    // Update next billing date
    subscription.nextBillingDate = subscription.calculateNextBillingDate();
    
    // Update accounting status
    subscription.accountingStatus = status === 'له' ? 'paid' : 'billed';

    await subscription.save();

    res.json({
      message: 'تم تجديد الاشتراك بنجاح',
      subscription
    });
  } catch (error) {
    console.error('Renew subscription error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// @route   GET /api/subscriptions/analytics/overview
// @desc    Get subscriptions analytics overview
// @access  Private (requires subscriptions view permission)
router.get('/analytics/overview', auth, checkPermission('subscriptions', 'view'), async (req, res) => {
  try {
    const totalSubscriptions = await Subscription.countDocuments();
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    const inactiveSubscriptions = await Subscription.countDocuments({ status: 'inactive' });
    const suspendedSubscriptions = await Subscription.countDocuments({ status: 'suspended' });
    const cancelledSubscriptions = await Subscription.countDocuments({ status: 'cancelled' });

    // Monthly revenue calculation
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);
    
    const nextMonth = new Date(currentMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const monthlyRevenue = await Subscription.aggregate([
      {
        $match: {
          status: 'active',
          nextBillingDate: { $gte: currentMonth, $lt: nextMonth }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$price.amount' }
        }
      }
    ]);

    res.json({
      totalSubscriptions,
      activeSubscriptions,
      inactiveSubscriptions,
      suspendedSubscriptions,
      cancelledSubscriptions,
      monthlyRevenue: monthlyRevenue[0]?.total || 0
    });
  } catch (error) {
    console.error('Get subscriptions analytics error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

module.exports = router;
