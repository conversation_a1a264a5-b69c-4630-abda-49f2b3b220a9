const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 9876;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/build')));

// Simple API routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'نظام إدارة الاشتراكات يعمل بنجاح!',
    timestamp: new Date().toISOString(),
    port: PORT
  });
});

app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'مرحباً بك في نظام إدارة الاشتراكات',
    features: [
      'إدارة الاشتراكات',
      'إدارة العملاء', 
      'إدارة الفواتير',
      'التقارير والتحليلات'
    ]
  });
});

// Mock data for testing
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    subscriptions: {
      total: 156,
      active: 142,
      thisMonth: 23,
      growth: 12
    },
    customers: {
      total: 89,
      thisMonth: 15,
      growth: 8
    },
    revenue: {
      thisMonth: 28500,
      growth: 22
    },
    invoices: {
      pending: 12,
      overdue: 3
    }
  });
});

// Serve React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/build/index.html'));
});

// Error handling
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    message: 'حدث خطأ في الخادم',
    error: err.message 
  });
});

app.listen(PORT, () => {
  console.log(`🚀 الخادم يعمل على المنفذ ${PORT}`);
  console.log(`🌐 الرابط: http://localhost:${PORT}`);
  console.log(`📊 API Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🧪 API Test: http://localhost:${PORT}/api/test`);
});
