import React, { createContext, useContext, useState, useEffect } from 'react';

// Theme configuration
const themes = {
  light: {
    name: 'light',
    colors: {
      primary: '#2563eb',
      primaryHover: '#1d4ed8',
      secondary: '#64748b',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      
      // Background colors
      background: '#ffffff',
      backgroundSecondary: '#f8fafc',
      backgroundTertiary: '#f1f5f9',
      
      // Text colors
      textPrimary: '#1e293b',
      textSecondary: '#64748b',
      textMuted: '#94a3b8',
      textInverse: '#ffffff',
      
      // Border colors
      border: '#e2e8f0',
      borderLight: '#f1f5f9',
      borderDark: '#cbd5e1',
      
      // Sidebar colors
      sidebarBg: '#1e293b',
      sidebarText: '#cbd5e1',
      sidebarTextActive: '#ffffff',
      sidebarHover: '#334155',
      
      // Card colors
      cardBg: '#ffffff',
      cardBorder: '#e2e8f0',
      cardShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      
      // Status colors
      statusActive: '#10b981',
      statusInactive: '#6b7280',
      statusPending: '#f59e0b',
      statusCancelled: '#ef4444',
      statusExpired: '#8b5cf6'
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      xxl: '3rem'
    },
    borderRadius: {
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem'
    }
  },
  dark: {
    name: 'dark',
    colors: {
      primary: '#3b82f6',
      primaryHover: '#2563eb',
      secondary: '#6b7280',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      
      // Background colors
      background: '#0f172a',
      backgroundSecondary: '#1e293b',
      backgroundTertiary: '#334155',
      
      // Text colors
      textPrimary: '#f8fafc',
      textSecondary: '#cbd5e1',
      textMuted: '#94a3b8',
      textInverse: '#1e293b',
      
      // Border colors
      border: '#334155',
      borderLight: '#475569',
      borderDark: '#1e293b',
      
      // Sidebar colors
      sidebarBg: '#0f172a',
      sidebarText: '#94a3b8',
      sidebarTextActive: '#f8fafc',
      sidebarHover: '#1e293b',
      
      // Card colors
      cardBg: '#1e293b',
      cardBorder: '#334155',
      cardShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2)',
      
      // Status colors
      statusActive: '#10b981',
      statusInactive: '#6b7280',
      statusPending: '#f59e0b',
      statusCancelled: '#ef4444',
      statusExpired: '#8b5cf6'
    },
    spacing: {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      xxl: '3rem'
    },
    borderRadius: {
      sm: '0.25rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px'
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem'
    }
  }
};

// Create context
const ThemeContext = createContext();

// Provider component
export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState(() => {
    const savedTheme = localStorage.getItem('theme');
    return savedTheme || 'light';
  });

  const [isRTL, setIsRTL] = useState(() => {
    const savedRTL = localStorage.getItem('isRTL');
    return savedRTL ? JSON.parse(savedRTL) : true; // Default to RTL for Arabic
  });

  // Update CSS variables when theme changes
  useEffect(() => {
    const theme = themes[currentTheme];
    const root = document.documentElement;

    // Set CSS custom properties
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });

    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value);
    });

    Object.entries(theme.fontSize).forEach(([key, value]) => {
      root.style.setProperty(`--text-${key}`, value);
    });

    // Set theme class on body
    document.body.className = `theme-${currentTheme}`;
    
    // Save to localStorage
    localStorage.setItem('theme', currentTheme);
  }, [currentTheme]);

  // Update RTL direction
  useEffect(() => {
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = isRTL ? 'ar' : 'en';
    localStorage.setItem('isRTL', JSON.stringify(isRTL));
  }, [isRTL]);

  const toggleTheme = () => {
    setCurrentTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const toggleRTL = () => {
    setIsRTL(prev => !prev);
  };

  const value = {
    theme: themes[currentTheme],
    currentTheme,
    setCurrentTheme,
    toggleTheme,
    isRTL,
    setIsRTL,
    toggleRTL,
    themes
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
